from django.db import models
from accounts.models import User
from products.models import Category

class Tip(models.Model):
    DIFFICULTY_LEVELS = [
        ('BEGINNER', 'Débutant'),
        ('INTERMEDIATE', 'Intermédiaire'),
        ('ADVANCED', 'Avancé'),
    ]
    
    title = models.CharField(max_length=200)
    slug = models.SlugField(unique=True)
    content = models.TextField()
    short_description = models.CharField(max_length=300)
    
    category = models.ForeignKey(Category, on_delete=models.CASCADE, related_name='tips')
    difficulty = models.CharField(max_length=15, choices=DIFFICULTY_LEVELS, default='BEGINNER')
    
    # Images et médias
    featured_image = models.ImageField(upload_to='tips/', blank=True, null=True)
    video_url = models.URLField(blank=True)
    
    # Compatibilité
    hair_types = models.CharField(max_length=100, blank=True, help_text="Types de cheveux compatibles (ex: 3A,3B,4A)")
    
    # Métadonnées
    is_featured = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
    views_count = models.IntegerField(default=0)
    likes_count = models.IntegerField(default=0)
    
    # SEO
    meta_description = models.CharField(max_length=160, blank=True)
    tags = models.CharField(max_length=200, blank=True, help_text="Tags séparés par des virgules")
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-created_at']
    
    def __str__(self):
        return self.title
    
    def increment_views(self):
        self.views_count += 1
        self.save(update_fields=['views_count'])

class TipLike(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='tip_likes')
    tip = models.ForeignKey(Tip, on_delete=models.CASCADE, related_name='likes')
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        unique_together = ['user', 'tip']
    
    def __str__(self):
        return f"{self.user.full_name} likes {self.tip.title}"

class TipFavorite(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='tip_favorites')
    tip = models.ForeignKey(Tip, on_delete=models.CASCADE, related_name='favorites')
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        unique_together = ['user', 'tip']
    
    def __str__(self):
        return f"{self.user.full_name} - {self.tip.title}"

class DailyTip(models.Model):
    """Astuce du jour"""
    tip = models.ForeignKey(Tip, on_delete=models.CASCADE)
    date = models.DateField(unique=True)
    is_active = models.BooleanField(default=True)
    
    class Meta:
        ordering = ['-date']
    
    def __str__(self):
        return f"Astuce du {self.date}: {self.tip.title}"

class TipStep(models.Model):
    """Étapes pour les astuces complexes"""
    tip = models.ForeignKey(Tip, on_delete=models.CASCADE, related_name='steps')
    step_number = models.IntegerField()
    title = models.CharField(max_length=200)
    description = models.TextField()
    image = models.ImageField(upload_to='tips/steps/', blank=True, null=True)
    
    class Meta:
        ordering = ['step_number']
        unique_together = ['tip', 'step_number']
    
    def __str__(self):
        return f"{self.tip.title} - Étape {self.step_number}"
