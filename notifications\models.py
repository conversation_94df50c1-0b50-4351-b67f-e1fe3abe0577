from django.db import models
from accounts.models import User
from fcm_django.models import FCMDevice

class NotificationTemplate(models.Model):
    NOTIFICATION_TYPES = [
        ('WELCOME', 'Bienvenue'),
        ('CHAT_RESPONSE', 'Réponse chat'),
        ('DAILY_TIP', 'Astuce du jour'),
        ('PRODUCT_RECOMMENDATION', 'Recommandation produit'),
        ('SUBSCRIPTION_REMINDER', 'Rappel abonnement'),
        ('TRIAL_EXPIRING', 'Essai expirant'),
        ('NEW_FEATURE', 'Nouvelle fonctionnalité'),
        ('MAINTENANCE', 'Maintenance'),
    ]
    
    name = models.CharField(max_length=100)
    notification_type = models.CharField(max_length=25, choices=NOTIFICATION_TYPES)
    title_template = models.CharField(max_length=200)
    body_template = models.TextField()
    
    # Configuration
    is_active = models.BooleanField(default=True)
    send_push = models.BooleanField(default=True)
    send_email = models.<PERSON>oleanField(default=False)
    send_in_app = models.BooleanField(default=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    def __str__(self):
        return f"{self.name} ({self.notification_type})"

class Notification(models.Model):
    STATUS_CHOICES = [
        ('PENDING', 'En attente'),
        ('SENT', 'Envoyé'),
        ('DELIVERED', 'Livré'),
        ('READ', 'Lu'),
        ('FAILED', 'Échec'),
    ]
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='notifications')
    template = models.ForeignKey(NotificationTemplate, on_delete=models.CASCADE, null=True, blank=True)
    
    title = models.CharField(max_length=200)
    body = models.TextField()
    
    # Métadonnées
    notification_type = models.CharField(max_length=25)
    data = models.JSONField(default=dict, blank=True)
    
    # Status
    status = models.CharField(max_length=10, choices=STATUS_CHOICES, default='PENDING')
    
    # Canaux
    push_sent = models.BooleanField(default=False)
    email_sent = models.BooleanField(default=False)
    in_app_sent = models.BooleanField(default=False)
    
    # Dates
    created_at = models.DateTimeField(auto_now_add=True)
    sent_at = models.DateTimeField(null=True, blank=True)
    read_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.title} - {self.user.full_name}"
    
    def mark_as_read(self):
        if not self.read_at:
            self.read_at = timezone.now()
            self.status = 'READ'
            self.save()

class UserNotificationSettings(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='notification_settings')
    
    # Préférences générales
    push_notifications = models.BooleanField(default=True)
    email_notifications = models.BooleanField(default=True)
    in_app_notifications = models.BooleanField(default=True)
    
    # Préférences par type
    chat_notifications = models.BooleanField(default=True)
    tip_notifications = models.BooleanField(default=True)
    product_notifications = models.BooleanField(default=True)
    subscription_notifications = models.BooleanField(default=True)
    marketing_notifications = models.BooleanField(default=False)
    
    # Horaires
    quiet_hours_start = models.TimeField(default='22:00')
    quiet_hours_end = models.TimeField(default='08:00')
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"Paramètres de {self.user.full_name}"
