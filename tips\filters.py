import django_filters
from .models import Tip
from products.models import Category

class TipFilter(django_filters.FilterSet):
    category = django_filters.ModelChoiceFilter(queryset=Category.objects.all())
    difficulty = django_filters.ChoiceFilter(choices=Tip.DIFFICULTY_LEVELS)
    hair_type = django_filters.CharFilter(field_name="hair_types", lookup_expr='icontains')
    is_featured = django_filters.BooleanFilter()
    has_video = django_filters.BooleanFilter(method='filter_has_video')
    
    class Meta:
        model = Tip
        fields = ['category', 'difficulty', 'hair_type', 'is_featured', 'has_video']
    
    def filter_has_video(self, queryset, name, value):
        if value:
            return queryset.exclude(video_url='')
        return queryset.filter(video_url='')
