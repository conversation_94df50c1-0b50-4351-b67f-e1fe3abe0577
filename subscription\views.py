from rest_framework import generics, status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django.utils import timezone
from .models import SubscriptionPlan, UserSubscription, PaymentHistory
from .serializers import SubscriptionPlanSerializer, UserSubscriptionSerializer, PaymentHistorySerializer

class SubscriptionPlansView(generics.ListAPIView):
    queryset = SubscriptionPlan.objects.filter(is_active=True)
    serializer_class = SubscriptionPlanSerializer
    permission_classes = [permissions.AllowAny]

class UserSubscriptionView(generics.RetrieveAPIView):
    serializer_class = UserSubscriptionSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_object(self):
        subscription, created = UserSubscription.objects.get_or_create(
            user=self.request.user,
            defaults={
                'plan': SubscriptionPlan.objects.get(plan_type='FREE_TRIAL'),
                'status': 'ACTIVE'
            }
        )
        return subscription

@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def start_trial(request):
    """Démarrer l'essai gratuit"""
    user = request.user
    
    if user.trial_started_at:
        return Response(
            {'error': 'Essai gratuit déjà utilisé'}, 
            status=status.HTTP_400_BAD_REQUEST
        )
    
    # Démarrer l'essai
    user.trial_started_at = timezone.now()
    user.subscription_type = 'FREE_TRIAL'
    user.save()
    
    # Créer l'abonnement
    trial_plan = SubscriptionPlan.objects.get(plan_type='FREE_TRIAL')
    subscription, created = UserSubscription.objects.get_or_create(
        user=user,
        defaults={
            'plan': trial_plan,
            'status': 'ACTIVE'
        }
    )
    
    return Response({
        'message': 'Essai gratuit démarré',
        'subscription': UserSubscriptionSerializer(subscription).data
    })

@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def subscribe(request):
    """S'abonner à un plan payant"""
    plan_id = request.data.get('plan_id')
    payment_method = request.data.get('payment_method')
    
    try:
        plan = SubscriptionPlan.objects.get(id=plan_id, is_active=True)
    except SubscriptionPlan.DoesNotExist:
        return Response(
            {'error': 'Plan non trouvé'}, 
            status=status.HTTP_404_NOT_FOUND
        )
    
    # Simuler le paiement (à remplacer par vraie intégration)
    payment_success = True  # Stripe, PayPal, etc.
    
    if payment_success:
        # Créer ou mettre à jour l'abonnement
        subscription, created = UserSubscription.objects.get_or_create(
            user=request.user,
            defaults={'plan': plan, 'status': 'ACTIVE'}
        )
        
        if not created:
            subscription.plan = plan
            subscription.status = 'ACTIVE'
            subscription.started_at = timezone.now()
            subscription.save()
        
        # Mettre à jour l'utilisateur
        request.user.is_premium = True
        request.user.subscription_type = plan.plan_type
        request.user.save()
        
        # Enregistrer le paiement
        PaymentHistory.objects.create(
            user=request.user,
            subscription=subscription,
            amount=plan.price,
            status='COMPLETED',
            payment_method=payment_method,
            transaction_id=f"TXN_{timezone.now().timestamp()}",
            completed_at=timezone.now()
        )
        
        return Response({
            'message': 'Abonnement activé',
            'subscription': UserSubscriptionSerializer(subscription).data
        })
    
    return Response(
        {'error': 'Échec du paiement'}, 
        status=status.HTTP_400_BAD_REQUEST
    )

class PaymentHistoryView(generics.ListAPIView):
    serializer_class = PaymentHistorySerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        return PaymentHistory.objects.filter(user=self.request.user)
