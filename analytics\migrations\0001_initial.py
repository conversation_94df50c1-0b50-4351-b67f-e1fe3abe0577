# Generated by Django 4.2.7 on 2025-06-26 07:47

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='DailyStats',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField(unique=True)),
                ('total_users', models.IntegerField(default=0)),
                ('new_users', models.IntegerField(default=0)),
                ('active_users', models.IntegerField(default=0)),
                ('premium_users', models.IntegerField(default=0)),
                ('total_sessions', models.IntegerField(default=0)),
                ('total_chat_messages', models.IntegerField(default=0)),
                ('total_voice_messages', models.IntegerField(default=0)),
                ('tips_viewed', models.IntegerField(default=0)),
                ('products_viewed', models.IntegerField(default=0)),
                ('total_revenue', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('new_subscriptions', models.IntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ['-date'],
            },
        ),
        migrations.CreateModel(
            name='ConversionFunnel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('app_opens', models.IntegerField(default=0)),
                ('registrations', models.IntegerField(default=0)),
                ('onboarding_completed', models.IntegerField(default=0)),
                ('first_chat', models.IntegerField(default=0)),
                ('trial_started', models.IntegerField(default=0)),
                ('subscription_purchased', models.IntegerField(default=0)),
            ],
            options={
                'ordering': ['-date'],
                'unique_together': {('date',)},
            },
        ),
        migrations.CreateModel(
            name='UserEvent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('event_type', models.CharField(choices=[('APP_OPEN', 'Ouverture app'), ('CHAT_START', 'Début chat'), ('CHAT_MESSAGE', 'Message chat'), ('VOICE_MESSAGE', 'Message vocal'), ('PRODUCT_VIEW', 'Vue produit'), ('TIP_VIEW', 'Vue astuce'), ('ONBOARDING_STEP', 'Étape onboarding'), ('SUBSCRIPTION_VIEW', 'Vue abonnement'), ('PAYMENT_ATTEMPT', 'Tentative paiement')], max_length=20)),
                ('event_data', models.JSONField(default=dict)),
                ('session_id', models.CharField(blank=True, max_length=100)),
                ('device_type', models.CharField(blank=True, max_length=50)),
                ('app_version', models.CharField(blank=True, max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='events', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['user', 'event_type'], name='analytics_u_user_id_e6357f_idx'), models.Index(fields=['created_at'], name='analytics_u_created_aac3ff_idx')],
            },
        ),
    ]
