from rest_framework import generics, status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import SearchFilter, OrderingFilter
from .models import Category, Brand, Product, ProductRecommendation, UserFavorite
from .serializers import (
    CategorySerializer, BrandSerializer, ProductListSerializer,
    ProductDetailSerializer, ProductRecommendationSerializer, UserFavoriteSerializer
)
from .filters import ProductFilter

class CategoryListView(generics.ListAPIView):
    queryset = Category.objects.filter(is_active=True)
    serializer_class = CategorySerializer
    permission_classes = [permissions.AllowAny]

class BrandListView(generics.ListAPIView):
    queryset = Brand.objects.filter(is_active=True)
    serializer_class = BrandSerializer
    permission_classes = [permissions.AllowAny]

class ProductListView(generics.ListAPIView):
    queryset = Product.objects.filter(is_active=True)
    serializer_class = ProductListSerializer
    permission_classes = [permissions.AllowAny]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_class = ProductFilter
    search_fields = ['name', 'brand__name', 'description']
    ordering_fields = ['price', 'rating', 'created_at']
    ordering = ['-created_at']

class ProductDetailView(generics.RetrieveAPIView):
    queryset = Product.objects.filter(is_active=True)
    serializer_class = ProductDetailSerializer
    permission_classes = [permissions.AllowAny]
    lookup_field = 'slug'

class FeaturedProductsView(generics.ListAPIView):
    queryset = Product.objects.filter(is_active=True, is_featured=True)
    serializer_class = ProductListSerializer
    permission_classes = [permissions.AllowAny]

class ProductRecommendationsView(generics.ListAPIView):
    serializer_class = ProductRecommendationSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        return ProductRecommendation.objects.filter(user=self.request.user)

class UserFavoritesView(generics.ListAPIView):
    serializer_class = UserFavoriteSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        return UserFavorite.objects.filter(user=self.request.user)

@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def toggle_favorite(request, product_id):
    try:
        product = Product.objects.get(id=product_id, is_active=True)
        favorite, created = UserFavorite.objects.get_or_create(
            user=request.user, product=product
        )
        
        if not created:
            favorite.delete()
            return Response({
                'message': 'Produit retiré des favoris',
                'is_favorited': False
            })
        else:
            return Response({
                'message': 'Produit ajouté aux favoris',
                'is_favorited': True
            })
    except Product.DoesNotExist:
        return Response(
            {'error': 'Produit non trouvé'}, 
            status=status.HTTP_404_NOT_FOUND
        )

@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def personalized_recommendations(request):
    """Génère des recommandations personnalisées basées sur le profil utilisateur"""
    user = request.user
    
    # Logique de recommandation basée sur le type de cheveux
    filters = {'is_active': True}
    
    if user.hair_type:
        if user.hair_type.startswith('3') or user.hair_type.startswith('4'):
            filters['hair_type_compatibility__in'] = ['ALL', '3A-4C', '3A-3C', '4A-4C']
        elif user.hair_type.startswith('2'):
            filters['hair_type_compatibility__in'] = ['ALL', '2A-2C']
        elif user.hair_type.startswith('1'):
            filters['hair_type_compatibility__in'] = ['ALL', '1A-1C']
    
    recommended_products = Product.objects.filter(**filters)[:10]
    serializer = ProductListSerializer(
        recommended_products, 
        many=True, 
        context={'request': request}
    )
    
    return Response({
        'message': 'Recommandations personnalisées',
        'products': serializer.data
    })
