from rest_framework import serializers
from .models import (
    OnboardingStep, OnboardingQuestion, QuestionOption,
    UserOnboardingProgress, UserOnboardingAnswer, HairAnalysisResult
)

class QuestionOptionSerializer(serializers.ModelSerializer):
    class Meta:
        model = QuestionOption
        fields = ['id', 'option_text', 'option_value', 'option_image', 'order']

class OnboardingQuestionSerializer(serializers.ModelSerializer):
    options = QuestionOptionSerializer(many=True, read_only=True)
    user_answer = serializers.SerializerMethodField()
    
    class Meta:
        model = OnboardingQuestion
        fields = [
            'id', 'question_number', 'question_type', 'question_text',
            'help_text', 'is_required', 'options', 'user_answer'
        ]
    
    def get_user_answer(self, obj):
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            try:
                answer = UserOnboardingAnswer.objects.get(
                    user=request.user, question=obj
                )
                return {
                    'text_answer': answer.text_answer,
                    'selected_options': answer.selected_options,
                    'scale_value': answer.scale_value
                }
            except UserOnboardingAnswer.DoesNotExist:
                return None
        return None

class OnboardingStepSerializer(serializers.ModelSerializer):
    questions = OnboardingQuestionSerializer(many=True, read_only=True)
    is_completed = serializers.SerializerMethodField()
    
    class Meta:
        model = OnboardingStep
        fields = [
            'id', 'step_number', 'step_type', 'title', 'description',
            'image', 'is_required', 'questions', 'is_completed'
        ]
    
    def get_is_completed(self, obj):
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            try:
                progress = UserOnboardingProgress.objects.get(user=request.user)
                return obj.id in progress.completed_steps
            except UserOnboardingProgress.DoesNotExist:
                return False
        return False

class UserOnboardingProgressSerializer(serializers.ModelSerializer):
    current_step = OnboardingStepSerializer(read_only=True)
    progress_percentage = serializers.ReadOnlyField()
    
    class Meta:
        model = UserOnboardingProgress
        fields = [
            'current_step', 'completed_steps', 'is_completed',
            'progress_percentage', 'started_at', 'completed_at'
        ]

class UserOnboardingAnswerSerializer(serializers.ModelSerializer):
    question = OnboardingQuestionSerializer(read_only=True)
    
    class Meta:
        model = UserOnboardingAnswer
        fields = [
            'id', 'question', 'text_answer', 'selected_options',
            'scale_value', 'created_at', 'updated_at'
        ]

class SubmitAnswerSerializer(serializers.Serializer):
    question_id = serializers.IntegerField()
    text_answer = serializers.CharField(required=False, allow_blank=True)
    selected_options = serializers.ListField(
        child=serializers.CharField(),
        required=False,
        default=list
    )
    scale_value = serializers.IntegerField(required=False, allow_null=True)
    
    def validate_question_id(self, value):
        try:
            OnboardingQuestion.objects.get(id=value)
        except OnboardingQuestion.DoesNotExist:
            raise serializers.ValidationError("Question non trouvée")
        return value

class HairAnalysisResultSerializer(serializers.ModelSerializer):
    hair_type_display = serializers.CharField(source='get_hair_type_detected_display', read_only=True)
    porosity_display = serializers.CharField(source='get_porosity_detected_display', read_only=True)
    
    class Meta:
        model = HairAnalysisResult
        fields = [
            'hair_type_detected', 'hair_type_display', 'porosity_detected',
            'porosity_display', 'recommended_routine', 'recommended_products',
            'care_tips', 'hair_type_confidence', 'porosity_confidence',
            'created_at', 'updated_at'
        ]
