# Generated by Django 4.2.7 on 2025-06-26 07:47

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Conversation',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('title', models.CharField(blank=True, max_length=200)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='conversations', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-updated_at'],
            },
        ),
        migrations.CreateModel(
            name='UserPreference',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('voice_speed', models.FloatField(default=1.0)),
                ('voice_pitch', models.FloatField(default=1.0)),
                ('preferred_voice', models.CharField(default='fr-FR-Wavenet-C', max_length=50)),
                ('conversation_style', models.CharField(choices=[('CASUAL', 'Décontracté'), ('FORMAL', 'Formel'), ('FRIENDLY', 'Amical')], default='FRIENDLY', max_length=20)),
                ('sound_notifications', models.BooleanField(default=True)),
                ('vibration_notifications', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='chat_preferences', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='Message',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('sender_type', models.CharField(choices=[('USER', 'Utilisateur'), ('AI', 'IA'), ('SYSTEM', 'Système')], max_length=10)),
                ('message_type', models.CharField(choices=[('TEXT', 'Texte'), ('VOICE', 'Vocal'), ('SYSTEM', 'Système')], default='TEXT', max_length=10)),
                ('content', models.TextField()),
                ('audio_file', models.FileField(blank=True, null=True, upload_to='chat/audio/')),
                ('ai_model', models.CharField(blank=True, max_length=50)),
                ('tokens_used', models.IntegerField(default=0)),
                ('processing_time', models.FloatField(default=0.0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('is_read', models.BooleanField(default=False)),
                ('conversation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='messages', to='chat.conversation')),
            ],
            options={
                'ordering': ['created_at'],
            },
        ),
        migrations.CreateModel(
            name='ChatSession',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('is_active', models.BooleanField(default=True)),
                ('started_at', models.DateTimeField(auto_now_add=True)),
                ('ended_at', models.DateTimeField(blank=True, null=True)),
                ('voice_enabled', models.BooleanField(default=True)),
                ('interruption_enabled', models.BooleanField(default=True)),
                ('conversation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sessions', to='chat.conversation')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='chat_sessions', to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
