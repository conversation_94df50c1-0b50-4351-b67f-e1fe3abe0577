import requests
from django.utils import timezone
from .models import MarketplaceSync, SyncLog, ExternalProductMapping
from products.models import Product, Brand, Category
import logging

logger = logging.getLogger(__name__)

class MarketplaceSyncService:
    def __init__(self, marketplace_id):
        self.marketplace = MarketplaceSync.objects.get(id=marketplace_id)
        self.sync_log = None
    
    def sync_all(self):
        """Synchronise tous les éléments"""
        self.sync_log = SyncLog.objects.create(
            marketplace=self.marketplace,
            status='PARTIAL'
        )
        
        try:
            if self.marketplace.sync_categories:
                self._sync_categories()
            
            if self.marketplace.sync_brands:
                self._sync_brands()
            
            if self.marketplace.sync_products:
                self._sync_products()
            
            self.sync_log.status = 'SUCCESS'
            self.sync_log.completed_at = timezone.now()
            self.sync_log.save()
            
            # Mettre à jour la dernière sync
            self.marketplace.last_sync = timezone.now()
            self.marketplace.save()
            
        except Exception as e:
            self.sync_log.status = 'ERROR'
            self.sync_log.error_message = str(e)
            self.sync_log.completed_at = timezone.now()
            self.sync_log.save()
            logger.error(f"Erreur sync marketplace: {e}")
    
    def _sync_categories(self):
        """Synchronise les catégories"""
        try:
            response = requests.get(
                f"{self.marketplace.api_url}/categories",
                headers=self._get_headers(),
                timeout=30
            )
            response.raise_for_status()
            
            categories_data = response.json()
            
            for cat_data in categories_data.get('categories', []):
                category, created = Category.objects.get_or_create(
                    slug=cat_data['slug'],
                    defaults={
                        'name': cat_data['name'],
                        'description': cat_data.get('description', ''),
                    }
                )
                
                if not created:
                    category.name = cat_data['name']
                    category.description = cat_data.get('description', '')
                    category.save()
                    
        except Exception as e:
            logger.error(f"Erreur sync catégories: {e}")
            raise
    
    def _sync_brands(self):
        """Synchronise les marques"""
        try:
            response = requests.get(
                f"{self.marketplace.api_url}/brands",
                headers=self._get_headers(),
                timeout=30
            )
            response.raise_for_status()
            
            brands_data = response.json()
            
            for brand_data in brands_data.get('brands', []):
                brand, created = Brand.objects.get_or_create(
                    slug=brand_data['slug'],
                    defaults={
                        'name': brand_data['name'],
                        'description': brand_data.get('description', ''),
                        'website': brand_data.get('website', ''),
                    }
                )
                
                if not created:
                    brand.name = brand_data['name']
                    brand.description = brand_data.get('description', '')
                    brand.website = brand_data.get('website', '')
                    brand.save()
                    
        except Exception as e:
            logger.error(f"Erreur sync marques: {e}")
            raise
    
    def _sync_products(self):
        """Synchronise les produits"""
        try:
            response = requests.get(
                f"{self.marketplace.api_url}/products",
                headers=self._get_headers(),
                timeout=30
            )
            response.raise_for_status()
            
            products_data = response.json()
            
            for product_data in products_data.get('products', []):
                self._sync_single_product(product_data)
                
        except Exception as e:
            logger.error(f"Erreur sync produits: {e}")
            raise
    
    def _sync_single_product(self, product_data):
        """Synchronise un produit individuel"""
        try:
            # Récupérer ou créer la marque et catégorie
            brand = Brand.objects.get(slug=product_data['brand_slug'])
            category = Category.objects.get(slug=product_data['category_slug'])
            
            # Vérifier si le produit existe déjà
            mapping = ExternalProductMapping.objects.filter(
                external_id=product_data['id'],
                marketplace=self.marketplace
            ).first()
            
            if mapping:
                # Mettre à jour le produit existant
                product = mapping.local_product
                product.name = product_data['name']
                product.description = product_data['description']
                product.price = product_data['price']
                product.purchase_url = product_data['url']
                product.save()
                
                self.sync_log.products_updated += 1
            else:
                # Créer un nouveau produit
                product = Product.objects.create(
                    name=product_data['name'],
                    slug=f"{product_data['slug']}-{product_data['id']}",
                    brand=brand,
                    category=category,
                    description=product_data['description'],
                    short_description=product_data.get('short_description', ''),
                    price=product_data['price'],
                    purchase_url=product_data['url'],
                    hair_type_compatibility=product_data.get('hair_types', 'ALL'),
                )
                
                # Créer le mapping
                ExternalProductMapping.objects.create(
                    local_product=product,
                    external_id=product_data['id'],
                    external_url=product_data['url'],
                    marketplace=self.marketplace
                )
                
                self.sync_log.products_created += 1
            
            self.sync_log.products_synced += 1
            self.sync_log.save()
            
        except Exception as e:
            self.sync_log.products_errors += 1
            self.sync_log.save()
            logger.error(f"Erreur sync produit {product_data.get('id')}: {e}")
    
    def _get_headers(self):
        """Headers pour les requêtes API"""
        headers = {
            'Content-Type': 'application/json',
            'User-Agent': 'CheveuxTextures/1.0'
        }
        
        if self.marketplace.api_key:
            headers['Authorization'] = f'Bearer {self.marketplace.api_key}'
        
        return headers
