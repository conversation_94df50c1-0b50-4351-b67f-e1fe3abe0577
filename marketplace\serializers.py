from rest_framework import serializers
from .models import SyncLog, MarketplaceSync

class SyncLogSerializer(serializers.ModelSerializer):
    marketplace_name = serializers.CharField(source='marketplace.name', read_only=True)
    
    class Meta:
        model = SyncLog
        fields = [
            'id', 'marketplace_name', 'status', 'products_synced',
            'products_created', 'products_updated', 'products_errors',
            'started_at', 'completed_at', 'error_message'
        ]
