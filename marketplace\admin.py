from django.contrib import admin
from .models import MarketplaceSync, SyncLog, ExternalProductMapping

@admin.register(MarketplaceSync)
class MarketplaceSyncAdmin(admin.ModelAdmin):
    list_display = ('name', 'is_active', 'last_sync', 'sync_frequency_hours')
    list_filter = ('is_active', 'last_sync')
    
    actions = ['trigger_sync']
    
    def trigger_sync(self, request, queryset):
        from .services import MarketplaceSyncService
        for marketplace in queryset:
            sync_service = MarketplaceSyncService(marketplace.id)
            sync_service.sync_all()
        self.message_user(request, f"Synchronisation déclenchée pour {queryset.count()} marketplace(s)")
    trigger_sync.short_description = "Déclencher la synchronisation"

@admin.register(SyncLog)
class SyncLogAdmin(admin.ModelAdmin):
    list_display = ('marketplace', 'status', 'products_synced', 'started_at', 'completed_at')
    list_filter = ('status', 'started_at', 'marketplace')
    readonly_fields = ('started_at', 'completed_at')

@admin.register(ExternalProductMapping)
class ExternalProductMappingAdmin(admin.ModelAdmin):
    list_display = ('local_product', 'external_id', 'marketplace', 'last_synced')
    list_filter = ('marketplace', 'sync_enabled', 'last_synced')
    search_fields = ('local_product__name', 'external_id')
