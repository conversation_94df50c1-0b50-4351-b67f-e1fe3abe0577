from django.urls import path
from . import views

urlpatterns = [
    path('categories/', views.CategoryListView.as_view(), name='category_list'),
    path('brands/', views.BrandListView.as_view(), name='brand_list'),
    path('', views.ProductListView.as_view(), name='product_list'),
    path('featured/', views.FeaturedProductsView.as_view(), name='featured_products'),
    path('recommendations/', views.ProductRecommendationsView.as_view(), name='product_recommendations'),
    path('personalized/', views.personalized_recommendations, name='personalized_recommendations'),
    path('favorites/', views.UserFavoritesView.as_view(), name='user_favorites'),
    path('<int:product_id>/toggle-favorite/', views.toggle_favorite, name='toggle_favorite'),
    path('<slug:slug>/', views.ProductDetailView.as_view(), name='product_detail'),
]
