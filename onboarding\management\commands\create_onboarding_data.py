from django.core.management.base import BaseCommand
from onboarding.models import OnboardingStep, OnboardingQuestion, QuestionOption

class Command(BaseCommand):
    help = 'Crée les données d\'onboarding selon les maquettes'
    
    def handle(self, *args, **options):
        self.stdout.write('Création des étapes d\'onboarding selon les maquettes...')
        
        # Étape 1: Bienvenue (selon maquette)
        welcome_step, created = OnboardingStep.objects.get_or_create(
            step_number=1,
            defaults={
                'step_type': 'WELCOME',
                'title': 'Bienvenue dans Cheveux Textures',
                'description': 'Un espace pensé pour toi, pour mieux comprendre, soigner et aimer tes cheveux texturés.',
                'is_required': True,
            }
        )
        
        # Étape 2: Conseils personnalisés (selon maquette)
        advice_step, created = OnboardingStep.objects.get_or_create(
            step_number=2,
            defaults={
                'step_type': 'PROFILE',
                'title': 'Des conseils qui te ressemblent',
                'description': 'Pose tes questions, partage tes habitudes, et reçois des recommandations précises selon ton type de cheveux, tes objectifs et ta routine. Tout est personnalisé, rien n\'est standard.',
                'is_required': True,
            }
        )
        
        # Étape 3: Analyse capillaire (selon maquette)
        analysis_step, created = OnboardingStep.objects.get_or_create(
            step_number=3,
            defaults={
                'step_type': 'HAIR_ANALYSIS',
                'title': 'Parlons un peu de tes cheveux',
                'description': 'Pour mieux t\'accompagner, j\'ai besoin de mieux te connaître. En quelques questions simples, on définit ensemble ton profil capillaire.',
                'is_required': True,
            }
        )
        
        if created:
            # Question sur la forme des cheveux (selon maquette)
            hair_shape_q = OnboardingQuestion.objects.create(
                step=analysis_step,
                question_number=1,
                question_type='SINGLE_CHOICE',
                question_text='Quelle est la forme naturelle de tes cheveux ?',
                is_required=True
            )
            
            options = [
                ('Raides', 'straight'),
                ('Ondulés', 'wavy'),
                ('Bouclés', 'curly'),
                ('Crépus', 'coily')
            ]
            
            for i, (text, value) in enumerate(options):
                QuestionOption.objects.create(
                    question=hair_shape_q,
                    option_text=text,
                    option_value=value,
                    order=i
                )
            
            # Question sur la texture
            texture_q = OnboardingQuestion.objects.create(
                step=analysis_step,
                question_number=2,
                question_type='SINGLE_CHOICE',
                question_text='Comment décrirais-tu la texture de tes cheveux ?',
                is_required=True
            )
            
            texture_options = [
                ('Fins', 'fine'),
                ('Moyens', 'medium'),
                ('Épais', 'thick')
            ]
            
            for i, (text, value) in enumerate(texture_options):
                QuestionOption.objects.create(
                    question=texture_q,
                    option_text=text,
                    option_value=value,
                    order=i
                )
        
        self.stdout.write(self.style.SUCCESS('Données d\'onboarding créées selon les maquettes!'))
