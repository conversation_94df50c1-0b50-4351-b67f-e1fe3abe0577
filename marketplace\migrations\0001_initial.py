# Generated by Django 4.2.7 on 2025-06-26 07:47

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('products', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='MarketplaceSync',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(default='Cheveux Crépus', max_length=100)),
                ('api_url', models.URLField()),
                ('api_key', models.CharField(blank=True, max_length=200)),
                ('sync_products', models.BooleanField(default=True)),
                ('sync_brands', models.BooleanField(default=True)),
                ('sync_categories', models.BooleanField(default=True)),
                ('sync_frequency_hours', models.IntegerField(default=24)),
                ('last_sync', models.DateTimeField(blank=True, null=True)),
                ('is_active', models.<PERSON>olean<PERSON>ield(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='SyncLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('SUCCESS', 'Succès'), ('ERROR', 'Erreur'), ('PARTIAL', 'Partiel')], max_length=10)),
                ('products_synced', models.IntegerField(default=0)),
                ('products_created', models.IntegerField(default=0)),
                ('products_updated', models.IntegerField(default=0)),
                ('products_errors', models.IntegerField(default=0)),
                ('log_details', models.TextField(blank=True)),
                ('error_message', models.TextField(blank=True)),
                ('started_at', models.DateTimeField(auto_now_add=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('marketplace', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sync_logs', to='marketplace.marketplacesync')),
            ],
            options={
                'ordering': ['-started_at'],
            },
        ),
        migrations.CreateModel(
            name='ExternalProductMapping',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('external_id', models.CharField(max_length=100)),
                ('external_url', models.URLField()),
                ('last_synced', models.DateTimeField(auto_now=True)),
                ('sync_enabled', models.BooleanField(default=True)),
                ('local_product', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='external_mapping', to='products.product')),
                ('marketplace', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='marketplace.marketplacesync')),
            ],
            options={
                'unique_together': {('external_id', 'marketplace')},
            },
        ),
    ]
