from django.core.management.base import BaseCommand
from notifications.models import NotificationTemplate

class Command(BaseCommand):
    help = 'Configure les templates de notifications'
    
    def handle(self, *args, **options):
        templates = [
            {
                'name': 'Bienvenue',
                'notification_type': 'WELCOME',
                'title_template': 'Bienvenue {{ user_name }} ! 🌟',
                'body_template': 'Ton voyage capillaire commence maintenant avec {{ app_name }}',
                'send_push': True,
                'send_email': False,
                'send_in_app': True,
            },
            {
                'name': 'Réponse Chat',
                'notification_type': 'CHAT_RESPONSE',
                'title_template': 'Nouvelle réponse de ton coach IA',
                'body_template': '{{ message_preview }}',
                'send_push': True,
                'send_email': False,
                'send_in_app': True,
            },
            {
                'name': 'Astuce du jour',
                'notification_type': 'DAILY_TIP',
                'title_template': 'Astuce du jour 💡',
                'body_template': 'Découvre: {{ tip_title }}',
                'send_push': True,
                'send_email': False,
                'send_in_app': True,
            },
            {
                'name': '<PERSON><PERSON><PERSON> expirant',
                'notification_type': 'TRIAL_EXPIRING',
                'title_template': 'Ton essai expire bientôt ⏰',
                'body_template': 'Plus que {{ days_remaining }} jour(s) d\'essai gratuit',
                'send_push': True,
                'send_email': True,
                'send_in_app': True,
            },
        ]
        
        for template_data in templates:
            template, created = NotificationTemplate.objects.get_or_create(
                notification_type=template_data['notification_type'],
                defaults=template_data
            )
            
            if created:
                self.stdout.write(
                    self.style.SUCCESS(f'Template créé: {template.name}')
                )
            else:
                self.stdout.write(
                    self.style.WARNING(f'Template existe déjà: {template.name}')
                )
