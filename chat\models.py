from django.db import models
from accounts.models import User
import uuid

class Conversation(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='conversations')
    title = models.CharField(max_length=200, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)
    
    class Meta:
        ordering = ['-updated_at']
    
    def __str__(self):
        return f"Conversation {self.id} - {self.user.full_name}"
    
    def save(self, *args, **kwargs):
        if not self.title:
            self.title = f"Conversation du {self.created_at.strftime('%d/%m/%Y')}"
        super().save(*args, **kwargs)

class Message(models.Model):
    MESSAGE_TYPES = [
        ('TEXT', 'Texte'),
        ('VOICE', 'Vocal'),
        ('SYSTEM', 'Système'),
    ]
    
    SENDER_TYPES = [
        ('USER', 'Utilisateur'),
        ('AI', 'IA'),
        ('SYSTEM', 'Système'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    conversation = models.ForeignKey(Conversation, on_delete=models.CASCADE, related_name='messages')
    sender_type = models.CharField(max_length=10, choices=SENDER_TYPES)
    message_type = models.CharField(max_length=10, choices=MESSAGE_TYPES, default='TEXT')
    
    content = models.TextField()
    audio_file = models.FileField(upload_to='chat/audio/', blank=True, null=True)
    
    # Métadonnées pour l'IA
    ai_model = models.CharField(max_length=50, blank=True)
    tokens_used = models.IntegerField(default=0)
    processing_time = models.FloatField(default=0.0)
    
    created_at = models.DateTimeField(auto_now_add=True)
    is_read = models.BooleanField(default=False)
    
    class Meta:
        ordering = ['created_at']
    
    def __str__(self):
        return f"{self.sender_type}: {self.content[:50]}..."

class ChatSession(models.Model):
    """Session de chat vocal en temps réel"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='chat_sessions')
    conversation = models.ForeignKey(Conversation, on_delete=models.CASCADE, related_name='sessions')
    
    is_active = models.BooleanField(default=True)
    started_at = models.DateTimeField(auto_now_add=True)
    ended_at = models.DateTimeField(null=True, blank=True)
    
    # Configuration de la session
    voice_enabled = models.BooleanField(default=True)
    interruption_enabled = models.BooleanField(default=True)
    
    def __str__(self):
        return f"Session {self.id} - {self.user.full_name}"

class UserPreference(models.Model):
    """Préférences utilisateur pour le chat"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='chat_preferences')
    
    # Préférences vocales
    voice_speed = models.FloatField(default=1.0)  # Vitesse de lecture
    voice_pitch = models.FloatField(default=1.0)  # Hauteur de voix
    preferred_voice = models.CharField(max_length=50, default='fr-FR-Wavenet-C')
    
    # Préférences de conversation
    conversation_style = models.CharField(
        max_length=20,
        choices=[
            ('CASUAL', 'Décontracté'),
            ('FORMAL', 'Formel'),
            ('FRIENDLY', 'Amical'),
        ],
        default='FRIENDLY'
    )
    
    # Notifications
    sound_notifications = models.BooleanField(default=True)
    vibration_notifications = models.BooleanField(default=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"Préférences de {self.user.full_name}"
