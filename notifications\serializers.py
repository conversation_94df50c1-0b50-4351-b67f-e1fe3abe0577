from rest_framework import serializers
from .models import Notification, UserNotificationSettings, NotificationTemplate

class NotificationSerializer(serializers.ModelSerializer):
    is_read = serializers.SerializerMethodField()
    
    class Meta:
        model = Notification
        fields = [
            'id', 'title', 'body', 'notification_type', 'data',
            'status', 'is_read', 'created_at', 'sent_at', 'read_at'
        ]
    
    def get_is_read(self, obj):
        return obj.read_at is not None

class UserNotificationSettingsSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserNotificationSettings
        fields = [
            'push_notifications', 'email_notifications', 'in_app_notifications',
            'chat_notifications', 'tip_notifications', 'product_notifications',
            'subscription_notifications', 'marketing_notifications',
            'quiet_hours_start', 'quiet_hours_end'
        ]

class NotificationTemplateSerializer(serializers.ModelSerializer):
    class Meta:
        model = NotificationTemplate
        fields = '__all__'
