from django.urls import path
from . import views

urlpatterns = [
    path('steps/', views.OnboardingStepsView.as_view(), name='onboarding_steps'),
    path('progress/', views.UserOnboardingProgressView.as_view(), name='onboarding_progress'),
    path('submit-answer/', views.submit_answer, name='submit_answer'),
    path('steps/<int:step_id>/complete/', views.complete_step, name='complete_step'),
    path('steps/<int:step_id>/skip/', views.skip_step, name='skip_step'),
    path('restart/', views.restart_onboarding, name='restart_onboarding'),
    path('summary/', views.onboarding_summary, name='onboarding_summary'),
    path('analysis/', views.HairAnalysisResultView.as_view(), name='hair_analysis'),
]
