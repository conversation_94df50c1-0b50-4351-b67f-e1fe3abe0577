from django.contrib import admin
from .models import SubscriptionPlan, UserSubscription, PaymentHistory

@admin.register(SubscriptionPlan)
class SubscriptionPlanAdmin(admin.ModelAdmin):
    list_display = ('name', 'plan_type', 'price', 'duration_days', 'is_active')
    list_filter = ('plan_type', 'is_active')
    search_fields = ('name',)

@admin.register(UserSubscription)
class UserSubscriptionAdmin(admin.ModelAdmin):
    list_display = ('user', 'plan', 'status', 'started_at', 'expires_at', 'is_active')
    list_filter = ('status', 'plan__plan_type', 'started_at')
    search_fields = ('user__email', 'user__first_name', 'user__last_name')
    readonly_fields = ('is_active',)

@admin.register(PaymentHistory)
class PaymentHistoryAdmin(admin.ModelAdmin):
    list_display = ('user', 'amount', 'status', 'payment_method', 'created_at')
    list_filter = ('status', 'payment_method', 'created_at')
    search_fields = ('user__email', 'transaction_id')
    readonly_fields = ('created_at', 'completed_at')
