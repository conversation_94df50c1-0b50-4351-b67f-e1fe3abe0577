from django.db import models
from accounts.models import User
from django.utils import timezone

class UserEvent(models.Model):
    EVENT_TYPES = [
        ('APP_OPEN', 'Ouverture app'),
        ('CHAT_START', 'Début chat'),
        ('CHAT_MESSAGE', 'Message chat'),
        ('VOICE_MESSAGE', 'Message vocal'),
        ('PRODUCT_VIEW', 'Vue produit'),
        ('TIP_VIEW', 'Vue astuce'),
        ('ONBOARDING_STEP', 'Étape onboarding'),
        ('SUBSCRIPTION_VIEW', 'Vue abonnement'),
        ('PAYMENT_ATTEMPT', 'Tentative paiement'),
    ]
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='events')
    event_type = models.Char<PERSON>ield(max_length=20, choices=EVENT_TYPES)
    event_data = models.JSONField(default=dict)
    
    # Métadonnées
    session_id = models.Char<PERSON>ield(max_length=100, blank=True)
    device_type = models.CharField(max_length=50, blank=True)
    app_version = models.CharField(max_length=20, blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'event_type']),
            models.Index(fields=['created_at']),
        ]
    
    def __str__(self):
        return f"{self.user.full_name} - {self.event_type}"

class DailyStats(models.Model):
    """Statistiques quotidiennes agrégées"""
    date = models.DateField(unique=True)
    
    # Utilisateurs
    total_users = models.IntegerField(default=0)
    new_users = models.IntegerField(default=0)
    active_users = models.IntegerField(default=0)
    premium_users = models.IntegerField(default=0)
    
    # Engagement
    total_sessions = models.IntegerField(default=0)
    total_chat_messages = models.IntegerField(default=0)
    total_voice_messages = models.IntegerField(default=0)
    
    # Contenu
    tips_viewed = models.IntegerField(default=0)
    products_viewed = models.IntegerField(default=0)
    
    # Revenus
    total_revenue = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    new_subscriptions = models.IntegerField(default=0)
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        ordering = ['-date']
    
    def __str__(self):
        return f"Stats {self.date}"

class ConversionFunnel(models.Model):
    """Entonnoir de conversion"""
    date = models.DateField()
    
    # Étapes du funnel
    app_opens = models.IntegerField(default=0)
    registrations = models.IntegerField(default=0)
    onboarding_completed = models.IntegerField(default=0)
    first_chat = models.IntegerField(default=0)
    trial_started = models.IntegerField(default=0)
    subscription_purchased = models.IntegerField(default=0)
    
    class Meta:
        unique_together = ['date']
        ordering = ['-date']
    
    def __str__(self):
        return f"Funnel {self.date}"
