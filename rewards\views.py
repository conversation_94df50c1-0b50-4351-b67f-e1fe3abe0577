from rest_framework import generics, status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django.utils import timezone
from .models import (
    PointsTransaction, UserPoints, Reward, UserReward,
    Challenge, UserChallenge, RewardProgram
)
from .serializers import (
    PointsTransactionSerializer, UserPointsSerializer, RewardSerializer,
    UserRewardSerializer, ChallengeSerializer, RewardProgramSerializer
)

class UserPointsView(generics.RetrieveAPIView):
    serializer_class = UserPointsSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_object(self):
        user_points, created = UserPoints.objects.get_or_create(
            user=self.request.user
        )
        return user_points

class PointsHistoryView(generics.ListAPIView):
    serializer_class = PointsTransactionSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        return PointsTransaction.objects.filter(user=self.request.user)

class RewardListView(generics.ListAPIView):
    serializer_class = RewardSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        return Reward.objects.filter(
            is_active=True,
            valid_from__lte=timezone.now()
        ).filter(
            models.Q(valid_until__isnull=True) | 
            models.Q(valid_until__gte=timezone.now())
        )

class UserRewardsView(generics.ListAPIView):
    serializer_class = UserRewardSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        return UserReward.objects.filter(user=self.request.user)

class ChallengeListView(generics.ListAPIView):
    serializer_class = ChallengeSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        now = timezone.now()
        return Challenge.objects.filter(
            is_active=True,
            start_date__lte=now,
            end_date__gte=now
        )

@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def redeem_reward(request, reward_id):
    """Échanger des points contre une récompense"""
    try:
        reward = Reward.objects.get(id=reward_id)
        
        if not reward.is_available:
            return Response(
                {'error': 'Récompense non disponible'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        user_points, created = UserPoints.objects.get_or_create(
            user=request.user
        )
        
        if user_points.available_points < reward.points_cost:
            return Response(
                {'error': 'Points insuffisants'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Vérifier le stock
        if reward.stock_quantity is not None:
            if reward.stock_quantity <= 0:
                return Response(
                    {'error': 'Stock épuisé'}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
            reward.stock_quantity -= 1
            reward.save()
        
        # Dépenser les points
        user_points.spend_points(
            reward.points_cost, 
            'REWARD_REDEEM', 
            f'Échange: {reward.name}'
        )
        
        # Créer la récompense utilisateur
        user_reward = UserReward.objects.create(
            user=request.user,
            reward=reward,
            status='ACTIVE'
        )
        
        return Response({
            'message': 'Récompense échangée avec succès',
            'user_reward': UserRewardSerializer(user_reward).data,
            'remaining_points': user_points.available_points
        })
        
    except Reward.DoesNotExist:
        return Response(
            {'error': 'Récompense non trouvée'}, 
            status=status.HTTP_404_NOT_FOUND
        )

@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def daily_checkin(request):
    """Connexion quotidienne pour gagner des points"""
    user_points, created = UserPoints.objects.get_or_create(
        user=request.user
    )
    
    today = timezone.now().date()
    
    # Vérifier si l'utilisateur s'est déjà connecté aujourd'hui
    if user_points.last_login_date == today:
        return Response({
            'message': 'Connexion quotidienne déjà effectuée',
            'points': user_points.available_points,
            'streak': user_points.daily_streak
        })
    
    # Mettre à jour la série et donner des points
    user_points.update_daily_streak()
    
    return Response({
        'message': 'Connexion quotidienne réussie',
        'points_earned': 10 if user_points.daily_streak == 1 else 5,
        'total_points': user_points.available_points,
        'streak': user_points.daily_streak
    })

@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def award_points(request):
    """Attribuer des points pour une action spécifique"""
    action_type = request.data.get('action_type')
    points = request.data.get('points', 0)
    description = request.data.get('description', '')
    
    if not action_type or points <= 0:
        return Response(
            {'error': 'Action et points requis'}, 
            status=status.HTTP_400_BAD_REQUEST
        )
    
    user_points, created = UserPoints.objects.get_or_create(
        user=request.user
    )
    
    # Vérifier les limites quotidiennes pour certaines actions
    daily_limits = {
        'CHAT_MESSAGE': 50,  # Max 50 points par jour pour les messages
        'TIP_LIKE': 20,      # Max 20 points par jour pour les likes
        'TIP_FAVORITE': 10,  # Max 10 points par jour pour les favoris
    }
    
    if action_type in daily_limits:
        today = timezone.now().date()
        daily_points = PointsTransaction.objects.filter(
            user=request.user,
            action_type=action_type,
            created_at__date=today,
            transaction_type='EARN'
        ).aggregate(
            total=models.Sum('points')
        )['total'] or 0
        
        if daily_points + points > daily_limits[action_type]:
            return Response({
                'message': 'Limite quotidienne atteinte pour cette action',
                'daily_limit': daily_limits[action_type],
                'current_points': daily_points
            })
    
    user_points.add_points(points, action_type, description)
    
    return Response({
        'message': 'Points attribués',
        'points_earned': points,
        'total_points': user_points.available_points
    })

@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def leaderboard(request):
    """Classement des utilisateurs par points"""
    top_users = UserPoints.objects.select_related('user').order_by(
        '-lifetime_points'
    )[:10]
    
    leaderboard_data = []
    for i, user_points in enumerate(top_users, 1):
        leaderboard_data.append({
            'rank': i,
            'user': {
                'id': user_points.user.id,
                'name': user_points.user.full_name,
                'profile_picture': user_points.user.profile_picture.url if user_points.user.profile_picture else None
            },
            'points': user_points.lifetime_points,
            'streak': user_points.daily_streak
        })
    
    # Position de l'utilisateur actuel
    user_points = UserPoints.objects.filter(user=request.user).first()
    user_rank = None
    if user_points:
        user_rank = UserPoints.objects.filter(
            lifetime_points__gt=user_points.lifetime_points
        ).count() + 1
    
    return Response({
        'leaderboard': leaderboard_data,
        'user_rank': user_rank,
        'user_points': user_points.lifetime_points if user_points else 0
    })
