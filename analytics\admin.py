from django.contrib import admin
from .models import UserEvent, DailyStats, ConversionFunnel

@admin.register(UserEvent)
class UserEventAdmin(admin.ModelAdmin):
    list_display = ('user', 'event_type', 'created_at', 'device_type')
    list_filter = ('event_type', 'device_type', 'created_at')
    search_fields = ('user__email', 'session_id')
    readonly_fields = ('created_at',)

@admin.register(DailyStats)
class DailyStatsAdmin(admin.ModelAdmin):
    list_display = ('date', 'total_users', 'new_users', 'active_users', 'total_revenue')
    list_filter = ('date',)
    readonly_fields = ('created_at',)

@admin.register(ConversionFunnel)
class ConversionFunnelAdmin(admin.ModelAdmin):
    list_display = ('date', 'app_opens', 'registrations', 'subscription_purchased')
    list_filter = ('date',)
