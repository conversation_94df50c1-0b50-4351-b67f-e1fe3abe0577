from django.utils import timezone
from django.template import Template, Context
from django.core.mail import send_mail
from django.conf import settings
from fcm_django.models import FCMDevice
from .models import Notification, NotificationTemplate, UserNotificationSettings
import logging

logger = logging.getLogger('api')

class NotificationService:
    """Service de gestion des notifications"""
    
    @staticmethod
    def send_notification(user, notification_type, context_data=None, template_id=None):
        """Envoie une notification à un utilisateur"""
        try:
            # Récupérer les paramètres utilisateur
            settings_obj, created = UserNotificationSettings.objects.get_or_create(user=user)
            
            # Récupérer le template
            if template_id:
                template = NotificationTemplate.objects.get(id=template_id)
            else:
                template = NotificationTemplate.objects.filter(
                    notification_type=notification_type,
                    is_active=True
                ).first()
            
            if not template:
                logger.error(f"Template non trouvé pour {notification_type}")
                return False
            
            # Générer le contenu
            context = Context(context_data or {})
            title = Template(template.title_template).render(context)
            body = Template(template.body_template).render(context)
            
            # Créer la notification
            notification = Notification.objects.create(
                user=user,
                template=template,
                title=title,
                body=body,
                notification_type=notification_type,
                data=context_data or {}
            )
            
            # Envoyer selon les préférences
            success = True
            
            # Push notification
            if template.send_push and settings_obj.push_notifications:
                success &= NotificationService._send_push(user, notification)
            
            # Email
            if template.send_email and settings_obj.email_notifications:
                success &= NotificationService._send_email(user, notification)
            
            # In-app (toujours envoyé)
            if template.send_in_app:
                notification.in_app_sent = True
            
            # Mettre à jour le statut
            notification.status = 'SENT' if success else 'FAILED'
            notification.sent_at = timezone.now()
            notification.save()
            
            return success
            
        except Exception as e:
            logger.error(f"Erreur envoi notification: {e}")
            return False
    
    @staticmethod
    def _send_push(user, notification):
        """Envoie une notification push"""
        try:
            devices = FCMDevice.objects.filter(user=user, active=True)
            if devices.exists():
                devices.send_message(
                    title=notification.title,
                    body=notification.body,
                    data=notification.data
                )
                notification.push_sent = True
                return True
        except Exception as e:
            logger.error(f"Erreur push notification: {e}")
        return False
    
    @staticmethod
    def _send_email(user, notification):
        """Envoie une notification par email"""
        try:
            send_mail(
                subject=notification.title,
                message=notification.body,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[user.email],
                fail_silently=False
            )
            notification.email_sent = True
            return True
        except Exception as e:
            logger.error(f"Erreur email notification: {e}")
        return False
    
    @staticmethod
    def send_welcome_notification(user):
        """Envoie la notification de bienvenue"""
        return NotificationService.send_notification(
            user=user,
            notification_type='WELCOME',
            context_data={
                'user_name': user.first_name,
                'app_name': 'Cheveux Texturés'
            }
        )
    
    @staticmethod
    def send_chat_response_notification(user, message_preview):
        """Envoie une notification de réponse chat"""
        return NotificationService.send_notification(
            user=user,
            notification_type='CHAT_RESPONSE',
            context_data={
                'user_name': user.first_name,
                'message_preview': message_preview[:50] + '...' if len(message_preview) > 50 else message_preview
            }
        )
    
    @staticmethod
    def send_daily_tip_notification(user, tip_title):
        """Envoie une notification d'astuce du jour"""
        return NotificationService.send_notification(
            user=user,
            notification_type='DAILY_TIP',
            context_data={
                'user_name': user.first_name,
                'tip_title': tip_title
            }
        )
    
    @staticmethod
    def send_trial_expiring_notification(user, days_remaining):
        """Envoie une notification d'essai expirant"""
        return NotificationService.send_notification(
            user=user,
            notification_type='TRIAL_EXPIRING',
            context_data={
                'user_name': user.first_name,
                'days_remaining': days_remaining
            }
        )
