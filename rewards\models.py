from django.db import models
from accounts.models import User
from django.utils import timezone
from datetime import timedelta

class RewardProgram(models.Model):
    name = models.CharField(max_length=100)
    description = models.TextField()
    is_active = models.BooleanField(default=True)
    start_date = models.DateTimeField()
    end_date = models.DateTimeField(null=True, blank=True)
    
    def __str__(self):
        return self.name

class PointsTransaction(models.Model):
    TRANSACTION_TYPES = [
        ('EARN', 'Gagné'),
        ('SPEND', 'Dépensé'),
        ('BONUS', 'Bonus'),
        ('PENALTY', 'Pénalité'),
    ]
    
    ACTION_TYPES = [
        ('DAILY_LOGIN', 'Connexion quotidienne'),
        ('CHAT_MESSAGE', 'Message dans le chat'),
        ('TIP_LIKE', 'Like sur astuce'),
        ('TIP_FAVORITE', 'Astuce en favori'),
        ('PRODUCT_FAVORITE', 'Produit en favori'),
        ('PROFILE_COMPLETE', 'Profil complété'),
        ('ONBOARDING_COMPLETE', 'Onboarding terminé'),
        ('REFERRAL', 'Parrainage'),
        ('REWARD_REDEEM', 'Échange de récompense'),
        ('PREMIUM_UPGRADE', 'Passage premium'),
    ]
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='points_transactions')
    transaction_type = models.CharField(max_length=10, choices=TRANSACTION_TYPES)
    action_type = models.CharField(max_length=20, choices=ACTION_TYPES)
    points = models.IntegerField()
    description = models.CharField(max_length=200)
    
    # Références optionnelles
    related_object_id = models.IntegerField(null=True, blank=True)
    related_object_type = models.CharField(max_length=50, blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.user.full_name} - {self.points} points ({self.get_transaction_type_display()})"

class UserPoints(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='points')
    total_points = models.IntegerField(default=0)
    available_points = models.IntegerField(default=0)
    lifetime_points = models.IntegerField(default=0)
    
    # Statistiques
    daily_streak = models.IntegerField(default=0)
    last_login_date = models.DateField(null=True, blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"{self.user.full_name} - {self.available_points} points"
    
    def add_points(self, points, action_type, description=""):
        """Ajoute des points à l'utilisateur"""
        self.total_points += points
        self.available_points += points
        self.lifetime_points += points
        self.save()
        
        # Créer la transaction
        PointsTransaction.objects.create(
            user=self.user,
            transaction_type='EARN',
            action_type=action_type,
            points=points,
            description=description or f"Points gagnés: {action_type}"
        )
    
    def spend_points(self, points, action_type, description=""):
        """Dépense des points"""
        if self.available_points >= points:
            self.available_points -= points
            self.save()
            
            PointsTransaction.objects.create(
                user=self.user,
                transaction_type='SPEND',
                action_type=action_type,
                points=points,
                description=description or f"Points dépensés: {action_type}"
            )
            return True
        return False
    
    def update_daily_streak(self):
        """Met à jour la série de connexions quotidiennes"""
        today = timezone.now().date()
        
        if self.last_login_date:
            if self.last_login_date == today - timedelta(days=1):
                # Connexion consécutive
                self.daily_streak += 1
            elif self.last_login_date != today:
                # Série interrompue
                self.daily_streak = 1
        else:
            # Première connexion
            self.daily_streak = 1
        
        self.last_login_date = today
        self.save()
        
        # Récompenser la connexion quotidienne
        if self.daily_streak == 1:
            self.add_points(10, 'DAILY_LOGIN', 'Connexion quotidienne')
        elif self.daily_streak % 7 == 0:
            # Bonus hebdomadaire
            self.add_points(50, 'DAILY_LOGIN', f'Bonus série de {self.daily_streak} jours')
        else:
            self.add_points(5, 'DAILY_LOGIN', f'Connexion quotidienne (série: {self.daily_streak})')

class Reward(models.Model):
    REWARD_TYPES = [
        ('DISCOUNT', 'Réduction'),
        ('FREEBIE', 'Cadeau gratuit'),
        ('PREMIUM', 'Accès premium'),
        ('CONSULTATION', 'Consultation'),
        ('EBOOK', 'E-book'),
        ('COURSE', 'Cours'),
    ]
    
    name = models.CharField(max_length=100)
    description = models.TextField()
    reward_type = models.CharField(max_length=15, choices=REWARD_TYPES)
    points_cost = models.IntegerField()
    
    # Détails de la récompense
    discount_percentage = models.IntegerField(null=True, blank=True)
    discount_amount = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    external_url = models.URLField(blank=True)
    coupon_code = models.CharField(max_length=50, blank=True)
    
    # Images
    image = models.ImageField(upload_to='rewards/', blank=True, null=True)
    
    # Disponibilité
    is_active = models.BooleanField(default=True)
    stock_quantity = models.IntegerField(null=True, blank=True, help_text="Laisser vide pour stock illimité")
    valid_from = models.DateTimeField()
    valid_until = models.DateTimeField(null=True, blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        ordering = ['points_cost']
    
    def __str__(self):
        return f"{self.name} ({self.points_cost} points)"
    
    @property
    def is_available(self):
        now = timezone.now()
        if not self.is_active:
            return False
        if now < self.valid_from:
            return False
        if self.valid_until and now > self.valid_until:
            return False
        if self.stock_quantity is not None and self.stock_quantity <= 0:
            return False
        return True

class UserReward(models.Model):
    STATUS_CHOICES = [
        ('PENDING', 'En attente'),
        ('ACTIVE', 'Actif'),
        ('USED', 'Utilisé'),
        ('EXPIRED', 'Expiré'),
    ]
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='rewards')
    reward = models.ForeignKey(Reward, on_delete=models.CASCADE, related_name='user_rewards')
    status = models.CharField(max_length=10, choices=STATUS_CHOICES, default='PENDING')
    
    # Code unique pour la récompense
    redemption_code = models.CharField(max_length=20, unique=True)
    
    # Dates
    redeemed_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField(null=True, blank=True)
    used_at = models.DateTimeField(null=True, blank=True)
    
    def __str__(self):
        return f"{self.user.full_name} - {self.reward.name}"
    
    def save(self, *args, **kwargs):
        if not self.redemption_code:
            import uuid
            self.redemption_code = str(uuid.uuid4())[:8].upper()
        super().save(*args, **kwargs)

class Challenge(models.Model):
    """Défis pour gagner des points"""
    name = models.CharField(max_length=100)
    description = models.TextField()
    points_reward = models.IntegerField()
    
    # Conditions du défi
    required_actions = models.JSONField(default=dict, help_text="Actions requises pour compléter le défi")
    
    # Disponibilité
    is_active = models.BooleanField(default=True)
    start_date = models.DateTimeField()
    end_date = models.DateTimeField()
    
    # Limites
    max_completions_per_user = models.IntegerField(default=1)
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    def __str__(self):
        return self.name

class UserChallenge(models.Model):
    """Progression des utilisateurs dans les défis"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='challenges')
    challenge = models.ForeignKey(Challenge, on_delete=models.CASCADE, related_name='user_challenges')
    
    progress = models.JSONField(default=dict)
    is_completed = models.BooleanField(default=False)
    completed_at = models.DateTimeField(null=True, blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        unique_together = ['user', 'challenge']
    
    def __str__(self):
        return f"{self.user.full_name} - {self.challenge.name}"
