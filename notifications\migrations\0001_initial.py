# Generated by Django 4.2.7 on 2025-06-26 07:47

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='NotificationTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('notification_type', models.CharField(choices=[('WELCOME', 'Bienvenue'), ('CHAT_RESPONSE', 'Réponse chat'), ('DAILY_TIP', 'Astuce du jour'), ('PRODUCT_RECOMMENDATION', 'Recommandation produit'), ('SUBSCRIPTION_REMINDER', 'Rappel abonnement'), ('TRIAL_EXPIRING', 'Essai expirant'), ('NEW_FEATURE', 'Nouvelle fonctionnalité'), ('MAINTENANCE', 'Maintenance')], max_length=25)),
                ('title_template', models.CharField(max_length=200)),
                ('body_template', models.TextField()),
                ('is_active', models.BooleanField(default=True)),
                ('send_push', models.BooleanField(default=True)),
                ('send_email', models.BooleanField(default=False)),
                ('send_in_app', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='UserNotificationSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('push_notifications', models.BooleanField(default=True)),
                ('email_notifications', models.BooleanField(default=True)),
                ('in_app_notifications', models.BooleanField(default=True)),
                ('chat_notifications', models.BooleanField(default=True)),
                ('tip_notifications', models.BooleanField(default=True)),
                ('product_notifications', models.BooleanField(default=True)),
                ('subscription_notifications', models.BooleanField(default=True)),
                ('marketing_notifications', models.BooleanField(default=False)),
                ('quiet_hours_start', models.TimeField(default='22:00')),
                ('quiet_hours_end', models.TimeField(default='08:00')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='notification_settings', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='Notification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('body', models.TextField()),
                ('notification_type', models.CharField(max_length=25)),
                ('data', models.JSONField(blank=True, default=dict)),
                ('status', models.CharField(choices=[('PENDING', 'En attente'), ('SENT', 'Envoyé'), ('DELIVERED', 'Livré'), ('READ', 'Lu'), ('FAILED', 'Échec')], default='PENDING', max_length=10)),
                ('push_sent', models.BooleanField(default=False)),
                ('email_sent', models.BooleanField(default=False)),
                ('in_app_sent', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('sent_at', models.DateTimeField(blank=True, null=True)),
                ('read_at', models.DateTimeField(blank=True, null=True)),
                ('template', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='notifications.notificationtemplate')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='notifications', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
    ]
