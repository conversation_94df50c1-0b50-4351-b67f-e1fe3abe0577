from rest_framework import serializers
from django.contrib.auth import authenticate
from django.contrib.auth.password_validation import validate_password
from django.utils import timezone
from .models import User, PasswordResetCode

class UserRegistrationSerializer(serializers.ModelSerializer):
    """Inscription selon la maquette"""
    nom = serializers.CharField(max_length=50, required=True)
    mail = serializers.EmailField(required=True)
    mot_de_passe = serializers.CharField(write_only=True, validators=[validate_password])
    
    class Meta:
        model = User
        fields = ('nom', 'mail', 'mot_de_passe')
    
    def validate_mail(self, value):
        if User.objects.filter(email=value).exists():
            raise serializers.ValidationError("Un compte avec cet email existe déjà.")
        return value
    
    def create(self, validated_data):
        # Créer l'utilisateur selon les champs de la maquette
        user = User.objects.create_user(
            username=validated_data['mail'],  # Username = email
            email=validated_data['mail'],
            nom=validated_data['nom'],
            mail=validated_data['mail'],
            password=validated_data['mot_de_passe']
        )
        
        # Générer le code de vérification à 6 chiffres
        verification_code = user.generate_email_verification_code()
        
        return user

class UserLoginSerializer(serializers.Serializer):
    """Connexion selon la maquette"""
    mail = serializers.EmailField(required=True)
    mot_de_passe = serializers.CharField(required=True)
    
    def validate(self, attrs):
        mail = attrs.get('mail')
        mot_de_passe = attrs.get('mot_de_passe')
        
        if mail and mot_de_passe:
            user = authenticate(username=mail, password=mot_de_passe)
            if not user:
                raise serializers.ValidationError('Identifiants invalides.')
            if not user.is_active:
                raise serializers.ValidationError('Compte désactivé.')
            attrs['user'] = user
        else:
            raise serializers.ValidationError('Mail et mot de passe requis.')
        
        return attrs

class EmailVerificationSerializer(serializers.Serializer):
    """Vérification email avec code 6 chiffres selon maquette"""
    code = serializers.CharField(max_length=6, min_length=6)
    
    def validate_code(self, value):
        # Vérifier que c'est bien 6 chiffres
        if not value.isdigit():
            raise serializers.ValidationError("Le code doit contenir uniquement des chiffres.")
        if len(value) != 6:
            raise serializers.ValidationError("Le code doit contenir exactement 6 chiffres.")
        return value

class PasswordResetRequestSerializer(serializers.Serializer):
    """Demande de réinitialisation selon maquette"""
    mail = serializers.EmailField(required=True)
    
    def validate_mail(self, value):
        try:
            User.objects.get(email=value)
        except User.DoesNotExist:
            raise serializers.ValidationError("Aucun compte associé à cet email.")
        return value

class PasswordResetVerifySerializer(serializers.Serializer):
    """Vérification du code de réinitialisation selon maquette"""
    mail = serializers.EmailField(required=True)
    code = serializers.CharField(max_length=6, min_length=6)
    
    def validate(self, attrs):
        mail = attrs.get('mail')
        code = attrs.get('code')
        
        # Vérifier que c'est 6 chiffres
        if not code.isdigit():
            raise serializers.ValidationError("Le code doit contenir uniquement des chiffres.")
        
        try:
            user = User.objects.get(email=mail)
            reset_code = PasswordResetCode.objects.get(
                user=user, code=code, is_used=False
            )
            if not reset_code.is_valid():
                raise serializers.ValidationError("Code expiré ou invalide.")
            attrs['reset_code'] = reset_code
            attrs['user'] = user
        except (User.DoesNotExist, PasswordResetCode.DoesNotExist):
            raise serializers.ValidationError("Code invalide.")
        
        return attrs

class PasswordResetConfirmSerializer(serializers.Serializer):
    """Changement de mot de passe après vérification selon maquette"""
    mail = serializers.EmailField(required=True)
    code = serializers.CharField(max_length=6, min_length=6)
    mot_de_passe = serializers.CharField(validators=[validate_password])
    confirmer_mot_de_passe = serializers.CharField()
    
    def validate(self, attrs):
        if attrs['mot_de_passe'] != attrs['confirmer_mot_de_passe']:
            raise serializers.ValidationError("Les mots de passe ne correspondent pas.")
        
        mail = attrs.get('mail')
        code = attrs.get('code')
        
        try:
            user = User.objects.get(email=mail)
            reset_code = PasswordResetCode.objects.get(
                user=user, code=code, is_used=False
            )
            if not reset_code.is_valid():
                raise serializers.ValidationError("Code expiré ou invalide.")
            attrs['user'] = user
            attrs['reset_code'] = reset_code
        except (User.DoesNotExist, PasswordResetCode.DoesNotExist):
            raise serializers.ValidationError("Code invalide.")
        
        return attrs

class ChangePasswordSerializer(serializers.Serializer):
    """Changement de mot de passe selon maquette"""
    mot_de_passe = serializers.CharField(validators=[validate_password])
    confirmer_mot_de_passe = serializers.CharField()
    
    def validate(self, attrs):
        if attrs['mot_de_passe'] != attrs['confirmer_mot_de_passe']:
            raise serializers.ValidationError("Les mots de passe ne correspondent pas.")
        return attrs

class UserProfileSerializer(serializers.ModelSerializer):
    """Profil utilisateur selon maquette"""
    full_name = serializers.ReadOnlyField()
    is_trial_expired = serializers.ReadOnlyField()
    can_chat = serializers.ReadOnlyField()
    hair_type_display = serializers.CharField(source='get_hair_type_display', read_only=True)
    hair_porosity_display = serializers.CharField(source='get_hair_porosity_display', read_only=True)
    
    class Meta:
        model = User
        fields = (
            'id', 'email', 'nom', 'mail', 'full_name',
            'phone', 'profile_picture', 'hair_type', 'hair_type_display',
            'hair_porosity', 'hair_porosity_display', 'hair_length', 
            'hair_concerns', 'current_routine', 'preferred_brands', 
            'budget_range', 'is_premium', 'subscription_type',
            'onboarding_completed', 'email_verified', 'is_trial_expired',
            'can_chat', 'daily_chat_limit', 'daily_chat_count', 'created_at'
        )
        read_only_fields = (
            'id', 'email', 'mail', 'is_premium', 'subscription_type', 
            'email_verified', 'created_at'
        )

class UserProfileUpdateSerializer(serializers.ModelSerializer):
    """Mise à jour profil selon maquette"""
    class Meta:
        model = User
        fields = (
            'nom', 'phone', 'profile_picture',
            'hair_type', 'hair_porosity', 'hair_length', 'hair_concerns',
            'current_routine', 'preferred_brands', 'budget_range'
        )
