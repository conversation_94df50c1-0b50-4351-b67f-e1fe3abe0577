from rest_framework import generics, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from .models import SyncLog
from .services import MarketplaceSyncService
from .serializers import SyncLogSerializer

@api_view(['POST'])
@permission_classes([permissions.IsAdminUser])
def trigger_sync(request, marketplace_id):
    """Déclencher une synchronisation manuelle"""
    try:
        sync_service = MarketplaceSyncService(marketplace_id)
        sync_service.sync_all()
        
        return Response({
            'message': 'Synchronisation démarrée',
            'sync_log_id': sync_service.sync_log.id
        })
    except Exception as e:
        return Response({
            'error': f'Erreur lors de la synchronisation: {str(e)}'
        }, status=400)

class SyncLogListView(generics.ListAPIView):
    queryset = SyncLog.objects.all()
    serializer_class = SyncLogSerializer
    permission_classes = [permissions.IsAdminUser]
