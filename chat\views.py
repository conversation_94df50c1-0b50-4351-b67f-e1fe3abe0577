from rest_framework import generics, status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from .models import Conversation, Message, ChatSession, UserPreference
from .serializers import (
    ConversationSerializer, ConversationListSerializer, MessageSerializer,
    ChatSessionSerializer, UserPreferenceSerializer, ChatMessageCreateSerializer
)
from .ai_service import AIService
import uuid

class ConversationListView(generics.ListCreateAPIView):
    serializer_class = ConversationListSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        return Conversation.objects.filter(user=self.request.user, is_active=True)
    
    def perform_create(self, serializer):
        serializer.save(user=self.request.user)

class ConversationDetailView(generics.RetrieveUpdateDestroyAPIView):
    serializer_class = ConversationSerializer
    permission_classes = [permissions.IsAuthenticated]
    lookup_field = 'id'
    
    def get_queryset(self):
        return Conversation.objects.filter(user=self.request.user)
    
    def perform_destroy(self, instance):
        instance.is_active = False
        instance.save()

class MessageListView(generics.ListAPIView):
    serializer_class = MessageSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        conversation_id = self.kwargs['conversation_id']
        conversation = get_object_or_404(
            Conversation, 
            id=conversation_id, 
            user=self.request.user
        )
        return Message.objects.filter(conversation=conversation)

@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def send_message(request, conversation_id):
    """Envoie un message et génère une réponse IA"""
    # Vérifier les limites pour utilisateurs gratuits
    if not request.user.can_chat:
        if request.user.is_trial_expired:
            return Response({
                'error': 'Essai gratuit expiré',
                'message': 'Votre essai gratuit a expiré. Abonnez-vous pour continuer à utiliser le chat.',
                'upgrade_required': True
            }, status=status.HTTP_402_PAYMENT_REQUIRED)
        else:
            return Response({
                'error': 'Limite quotidienne atteinte',
                'message': f'Vous avez atteint votre limite de {request.user.daily_chat_limit} messages par jour.',
                'daily_limit': request.user.daily_chat_limit,
                'upgrade_required': True
            }, status=status.HTTP_429_TOO_MANY_REQUESTS)

    # Incrémenter le compteur de chat
    request.user.increment_chat_count()
    
    try:
        conversation = Conversation.objects.get(
            id=conversation_id, 
            user=request.user
        )
    except Conversation.DoesNotExist:
        return Response(
            {'error': 'Conversation non trouvée'}, 
            status=status.HTTP_404_NOT_FOUND
        )
    
    serializer = ChatMessageCreateSerializer(data=request.data)
    if not serializer.is_valid():
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    # Créer le message utilisateur
    user_message = Message.objects.create(
        conversation=conversation,
        sender_type='USER',
        message_type=serializer.validated_data['message_type'],
        content=serializer.validated_data['content'],
        audio_file=serializer.validated_data.get('audio_file')
    )
    
    # Générer la réponse IA
    ai_service = AIService()
    ai_response = ai_service.generate_response(
        user_message=serializer.validated_data['content'],
        user_profile=request.user,
        conversation_history=conversation.messages.all()[:10]  # Derniers 10 messages
    )
    
    # Créer le message IA
    ai_message = Message.objects.create(
        conversation=conversation,
        sender_type='AI',
        message_type='TEXT',
        content=ai_response['content'],
        ai_model=ai_response.get('model', 'gpt-3.5-turbo'),
        tokens_used=ai_response.get('tokens_used', 0),
        processing_time=ai_response.get('processing_time', 0.0)
    )
    
    # Mettre à jour la conversation
    conversation.save()  # Met à jour updated_at
    
    return Response({
        'user_message': MessageSerializer(user_message).data,
        'ai_message': MessageSerializer(ai_message).data
    })

@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def start_chat_session(request):
    """Démarre une nouvelle session de chat vocal"""
    # Créer ou récupérer une conversation active
    conversation, created = Conversation.objects.get_or_create(
        user=request.user,
        is_active=True,
        defaults={'title': 'Nouvelle conversation'}
    )
    
    # Créer une session de chat
    session = ChatSession.objects.create(
        user=request.user,
        conversation=conversation,
        voice_enabled=request.data.get('voice_enabled', True),
        interruption_enabled=request.data.get('interruption_enabled', True)
    )
    
    return Response({
        'session': ChatSessionSerializer(session).data,
        'conversation': ConversationSerializer(conversation).data
    })

@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def end_chat_session(request, session_id):
    """Termine une session de chat"""
    try:
        session = ChatSession.objects.get(
            id=session_id, 
            user=request.user, 
            is_active=True
        )
        session.is_active = False
        session.ended_at = timezone.now()
        session.save()
        
        return Response({'message': 'Session terminée'})
    except ChatSession.DoesNotExist:
        return Response(
            {'error': 'Session non trouvée'}, 
            status=status.HTTP_404_NOT_FOUND
        )

class UserPreferenceView(generics.RetrieveUpdateAPIView):
    serializer_class = UserPreferenceSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_object(self):
        preference, created = UserPreference.objects.get_or_create(
            user=self.request.user
        )
        return preference

@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def chat_statistics(request):
    """Statistiques de chat pour l'utilisateur"""
    user = request.user
    conversations = Conversation.objects.filter(user=user)
    messages = Message.objects.filter(conversation__user=user)
    
    stats = {
        'total_conversations': conversations.count(),
        'active_conversations': conversations.filter(is_active=True).count(),
        'total_messages': messages.count(),
        'user_messages': messages.filter(sender_type='USER').count(),
        'ai_messages': messages.filter(sender_type='AI').count(),
        'total_tokens_used': sum(messages.values_list('tokens_used', flat=True)),
    }
    
    return Response(stats)
