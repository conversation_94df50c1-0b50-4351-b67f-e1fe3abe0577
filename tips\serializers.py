from rest_framework import serializers
from .models import Tip, Tip<PERSON>ike, Tip<PERSON>avorite, DailyTip, TipStep
from products.serializers import CategorySerializer

class TipStepSerializer(serializers.ModelSerializer):
    class Meta:
        model = TipStep
        fields = ['step_number', 'title', 'description', 'image']

class TipListSerializer(serializers.ModelSerializer):
    category = CategorySerializer(read_only=True)
    is_liked = serializers.SerializerMethodField()
    is_favorited = serializers.SerializerMethodField()
    
    class Meta:
        model = Tip
        fields = [
            'id', 'title', 'slug', 'short_description', 'category',
            'difficulty', 'featured_image', 'hair_types', 'is_featured',
            'views_count', 'likes_count', 'is_liked', 'is_favorited',
            'created_at'
        ]
    
    def get_is_liked(self, obj):
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            return TipLike.objects.filter(user=request.user, tip=obj).exists()
        return False
    
    def get_is_favorited(self, obj):
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            return TipFavorite.objects.filter(user=request.user, tip=obj).exists()
        return False

class TipDetailSerializer(serializers.ModelSerializer):
    category = CategorySerializer(read_only=True)
    steps = TipStepSerializer(many=True, read_only=True)
    is_liked = serializers.SerializerMethodField()
    is_favorited = serializers.SerializerMethodField()
    tags_list = serializers.SerializerMethodField()
    
    class Meta:
        model = Tip
        fields = [
            'id', 'title', 'slug', 'content', 'short_description',
            'category', 'difficulty', 'featured_image', 'video_url',
            'hair_types', 'is_featured', 'views_count', 'likes_count',
            'meta_description', 'tags', 'tags_list', 'steps',
            'is_liked', 'is_favorited', 'created_at', 'updated_at'
        ]
    
    def get_is_liked(self, obj):
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            return TipLike.objects.filter(user=request.user, tip=obj).exists()
        return False
    
    def get_is_favorited(self, obj):
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            return TipFavorite.objects.filter(user=request.user, tip=obj).exists()
        return False
    
    def get_tags_list(self, obj):
        if obj.tags:
            return [tag.strip() for tag in obj.tags.split(',')]
        return []

class DailyTipSerializer(serializers.ModelSerializer):
    tip = TipListSerializer(read_only=True)
    
    class Meta:
        model = DailyTip
        fields = ['id', 'tip', 'date', 'is_active']

class TipLikeSerializer(serializers.ModelSerializer):
    tip = TipListSerializer(read_only=True)
    
    class Meta:
        model = TipLike
        fields = ['id', 'tip', 'created_at']

class TipFavoriteSerializer(serializers.ModelSerializer):
    tip = TipListSerializer(read_only=True)
    
    class Meta:
        model = TipFavorite
        fields = ['id', 'tip', 'created_at']
