from rest_framework import generics, permissions, status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django.utils import timezone
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from .models import Notification, UserNotificationSettings
from .serializers import NotificationSerializer, UserNotificationSettingsSerializer

class NotificationListView(generics.ListAPIView):
    """Liste des notifications de l'utilisateur"""
    serializer_class = NotificationSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        return Notification.objects.filter(user=self.request.user)
    
    @swagger_auto_schema(
        operation_description="Récupérer les notifications de l'utilisateur",
        responses={200: NotificationSerializer(many=True)}
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)

class UserNotificationSettingsView(generics.RetrieveUpdateAPIView):
    """Paramètres de notification de l'utilisateur"""
    serializer_class = UserNotificationSettingsSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_object(self):
        settings_obj, created = UserNotificationSettings.objects.get_or_create(
            user=self.request.user
        )
        return settings_obj
    
    @swagger_auto_schema(
        operation_description="Récupérer les paramètres de notification",
        responses={200: UserNotificationSettingsSerializer}
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)
    
    @swagger_auto_schema(
        operation_description="Mettre à jour les paramètres de notification",
        request_body=UserNotificationSettingsSerializer,
        responses={200: UserNotificationSettingsSerializer}
    )
    def patch(self, request, *args, **kwargs):
        return super().patch(request, *args, **kwargs)

@swagger_auto_schema(
    method='post',
    operation_description="Marquer une notification comme lue",
    responses={
        200: "Notification marquée comme lue",
        404: "Notification non trouvée"
    }
)
@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def mark_notification_read(request, notification_id):
    """Marquer une notification comme lue"""
    try:
        notification = Notification.objects.get(
            id=notification_id,
            user=request.user
        )
        notification.mark_as_read()
        return Response({'message': 'Notification marquée comme lue'})
    except Notification.DoesNotExist:
        return Response(
            {'error': 'Notification non trouvée'},
            status=status.HTTP_404_NOT_FOUND
        )

@swagger_auto_schema(
    method='post',
    operation_description="Marquer toutes les notifications comme lues",
    responses={200: "Toutes les notifications marquées comme lues"}
)
@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def mark_all_notifications_read(request):
    """Marquer toutes les notifications comme lues"""
    notifications = Notification.objects.filter(
        user=request.user,
        read_at__isnull=True
    )
    
    count = notifications.update(
        read_at=timezone.now(),
        status='READ'
    )
    
    return Response({
        'message': f'{count} notifications marquées comme lues'
    })

@swagger_auto_schema(
    method='get',
    operation_description="Compter les notifications non lues",
    responses={
        200: openapi.Response(
            description="Nombre de notifications non lues",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'unread_count': openapi.Schema(type=openapi.TYPE_INTEGER)
                }
            )
        )
    }
)
@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def unread_count(request):
    """Compter les notifications non lues"""
    count = Notification.objects.filter(
        user=request.user,
        read_at__isnull=True
    ).count()
    
    return Response({'unread_count': count})
