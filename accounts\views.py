from rest_framework import status, generics, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework_simplejwt.tokens import RefreshToken
from django.core.mail import send_mail
from django.conf import settings
from django.template.loader import render_to_string
from django.utils.html import strip_tags
from django.utils import timezone
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
import logging

from .models import User, PasswordResetCode
from .serializers import (
    UserRegistrationSerializer, UserLoginSerializer, UserProfileSerializer,
    UserProfileUpdateSerializer, PasswordResetRequestSerializer, 
    PasswordResetVerifySerializer, PasswordResetConfirmSerializer, 
    ChangePasswordSerializer, EmailVerificationSerializer
)

logger = logging.getLogger('api')

@swagger_auto_schema(
    method='post',
    operation_description="Inscription d'un nouvel utilisateur selon maquette",
    request_body=openapi.Schema(
        type=openapi.TYPE_OBJECT,
        required=['nom', 'mail', 'mot_de_passe'],
        properties={
            'nom': openapi.Schema(type=openapi.TYPE_STRING, description='Nom complet'),
            'mail': openapi.Schema(type=openapi.TYPE_STRING, format='email', description='Adresse email'),
            'mot_de_passe': openapi.Schema(type=openapi.TYPE_STRING, description='Mot de passe (min 8 chars, maj, min, spéciaux)'),
        }
    ),
    responses={
        201: openapi.Response(
            description="Compte créé avec succès",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'message': openapi.Schema(type=openapi.TYPE_STRING),
                    'user': openapi.Schema(type=openapi.TYPE_OBJECT),
                    'verification_code_sent': openapi.Schema(type=openapi.TYPE_BOOLEAN),
                    'tokens': openapi.Schema(
                        type=openapi.TYPE_OBJECT,
                        properties={
                            'refresh': openapi.Schema(type=openapi.TYPE_STRING),
                            'access': openapi.Schema(type=openapi.TYPE_STRING),
                        }
                    ),
                }
            )
        ),
        400: "Erreurs de validation"
    }
)
@api_view(['POST'])
@permission_classes([permissions.AllowAny])
def register(request):
    """
    Inscription d'un nouvel utilisateur selon la maquette
    
    Champs requis selon maquette:
    - nom: Nom complet de l'utilisateur
    - mail: Adresse email
    - mot_de_passe: Mot de passe sécurisé
    
    Envoie un code de vérification à 6 chiffres par email.
    """
    serializer = UserRegistrationSerializer(data=request.data)
    if serializer.is_valid():
        user = serializer.save()
        
        # Envoyer le code de vérification par email
        try:
            send_verification_code_email(user, user.email_verification_code)
            logger.info(f"Code de vérification envoyé à {user.email}")
            verification_sent = True
        except Exception as e:
            logger.error(f"Erreur envoi code vérification: {e}")
            verification_sent = False
        
        # Générer les tokens JWT
        refresh = RefreshToken.for_user(user)
        
        logger.info(f"Nouvel utilisateur inscrit: {user.email}")
        
        return Response({
            'message': 'Compte créé avec succès. Un code de vérification a été envoyé à votre email.',
            'user': UserProfileSerializer(user).data,
            'verification_code_sent': verification_sent,
            'tokens': {
                'refresh': str(refresh),
                'access': str(refresh.access_token),
            }
        }, status=status.HTTP_201_CREATED)
    
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

@swagger_auto_schema(
    method='post',
    operation_description="Connexion utilisateur selon maquette",
    request_body=openapi.Schema(
        type=openapi.TYPE_OBJECT,
        required=['mail', 'mot_de_passe'],
        properties={
            'mail': openapi.Schema(type=openapi.TYPE_STRING, format='email'),
            'mot_de_passe': openapi.Schema(type=openapi.TYPE_STRING),
        }
    ),
    responses={
        200: "Connexion réussie",
        400: "Identifiants invalides"
    }
)
@api_view(['POST'])
@permission_classes([permissions.AllowAny])
def login(request):
    """Connexion utilisateur selon maquette"""
    serializer = UserLoginSerializer(data=request.data)
    if serializer.is_valid():
        user = serializer.validated_data['user']
        refresh = RefreshToken.for_user(user)
        
        logger.info(f"Connexion utilisateur: {user.email}")
        
        return Response({
            'message': 'Connexion réussie',
            'user': UserProfileSerializer(user).data,
            'tokens': {
                'refresh': str(refresh),
                'access': str(refresh.access_token),
            }
        })
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

@swagger_auto_schema(
    method='post',
    operation_description="Vérification de l'email avec code 6 chiffres selon maquette",
    request_body=openapi.Schema(
        type=openapi.TYPE_OBJECT,
        required=['code'],
        properties={
            'code': openapi.Schema(type=openapi.TYPE_STRING, description='Code à 6 chiffres reçu par email'),
        }
    ),
    responses={
        200: "Email vérifié avec succès",
        400: "Code invalide ou expiré"
    }
)
@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def verify_email(request):
    """Vérification de l'email avec code 6 chiffres selon maquette"""
    serializer = EmailVerificationSerializer(data=request.data)
    if serializer.is_valid():
        code = serializer.validated_data['code']
        user = request.user
        
        if user.is_verification_code_valid(code):
            user.email_verified = True
            user.email_verification_code = ''
            user.verification_code_expires = None
            user.save()
            
            logger.info(f"Email vérifié pour: {user.email}")
            
            return Response({
                'message': 'Email vérifié avec succès',
                'email_verified': True
            })
        else:
            return Response({
                'error': 'Code invalide ou expiré'
            }, status=status.HTTP_400_BAD_REQUEST)
    
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

@swagger_auto_schema(
    method='post',
    operation_description="Renvoyer le code de vérification email",
    responses={
        200: "Code renvoyé",
        400: "Erreur"
    }
)
@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def resend_verification_code(request):
    """Renvoyer le code de vérification selon maquette"""
    user = request.user
    
    if user.email_verified:
        return Response({
            'message': 'Email déjà vérifié'
        })
    
    # Générer un nouveau code
    verification_code = user.generate_email_verification_code()
    
    # Envoyer par email
    try:
        send_verification_code_email(user, verification_code)
        logger.info(f"Code de vérification renvoyé à {user.email}")
        
        return Response({
            'message': 'Code de vérification renvoyé'
        })
    except Exception as e:
        logger.error(f"Erreur renvoi code: {e}")
        return Response({
            'error': 'Erreur lors de l\'envoi du code'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@swagger_auto_schema(
    method='post',
    operation_description="Demande de réinitialisation de mot de passe selon maquette",
    request_body=openapi.Schema(
        type=openapi.TYPE_OBJECT,
        required=['mail'],
        properties={
            'mail': openapi.Schema(type=openapi.TYPE_STRING, format='email'),
        }
    ),
    responses={
        200: "Code envoyé",
        400: "Email invalide"
    }
)
@api_view(['POST'])
@permission_classes([permissions.AllowAny])
def password_reset_request(request):
    """Demande de réinitialisation de mot de passe selon maquette"""
    serializer = PasswordResetRequestSerializer(data=request.data)
    if serializer.is_valid():
        mail = serializer.validated_data['mail']
        user = User.objects.get(email=mail)
        
        # Invalider les anciens codes
        PasswordResetCode.objects.filter(user=user, is_used=False).update(is_used=True)
        
        # Créer un nouveau code à 6 chiffres
        reset_code = PasswordResetCode.objects.create(user=user)
        
        # Envoyer l'email avec le code
        try:
            send_password_reset_email(user, reset_code.code)
            logger.info(f"Code de réinitialisation envoyé à {user.email}")
        except Exception as e:
            logger.error(f"Erreur envoi email reset: {e}")
        
        return Response({
            'message': 'Code de vérification envoyé par email'
        })
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

@swagger_auto_schema(
    method='post',
    operation_description="Vérification du code de réinitialisation selon maquette",
    request_body=openapi.Schema(
        type=openapi.TYPE_OBJECT,
        required=['mail', 'code'],
        properties={
            'mail': openapi.Schema(type=openapi.TYPE_STRING, format='email'),
            'code': openapi.Schema(type=openapi.TYPE_STRING, description='Code à 6 chiffres'),
        }
    ),
    responses={
        200: "Code vérifié",
        400: "Code invalide"
    }
)
@api_view(['POST'])
@permission_classes([permissions.AllowAny])
def password_reset_verify(request):
    """Vérification du code de réinitialisation selon maquette"""
    serializer = PasswordResetVerifySerializer(data=request.data)
    if serializer.is_valid():
        return Response({
            'message': 'Code vérifié avec succès',
            'code_valid': True
        })
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

@swagger_auto_schema(
    method='post',
    operation_description="Changement de mot de passe après vérification selon maquette",
    request_body=openapi.Schema(
        type=openapi.TYPE_OBJECT,
        required=['mail', 'code', 'mot_de_passe', 'confirmer_mot_de_passe'],
        properties={
            'mail': openapi.Schema(type=openapi.TYPE_STRING, format='email'),
            'code': openapi.Schema(type=openapi.TYPE_STRING, description='Code à 6 chiffres'),
            'mot_de_passe': openapi.Schema(type=openapi.TYPE_STRING),
            'confirmer_mot_de_passe': openapi.Schema(type=openapi.TYPE_STRING),
        }
    ),
    responses={
        200: "Mot de passe réinitialisé",
        400: "Erreur de validation"
    }
)
@api_view(['POST'])
@permission_classes([permissions.AllowAny])
def password_reset_confirm(request):
    """Changement de mot de passe après vérification selon maquette"""
    serializer = PasswordResetConfirmSerializer(data=request.data)
    if serializer.is_valid():
        user = serializer.validated_data['user']
        reset_code = serializer.validated_data['reset_code']
        new_password = serializer.validated_data['mot_de_passe']
        
        user.set_password(new_password)
        user.save()
        
        reset_code.is_used = True
        reset_code.save()
        
        logger.info(f"Mot de passe réinitialisé pour: {user.email}")
        
        return Response({
            'message': 'Mot de passe réinitialisé avec succès'
        })
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

@swagger_auto_schema(
    method='post',
    operation_description="Changement de mot de passe selon maquette",
    request_body=openapi.Schema(
        type=openapi.TYPE_OBJECT,
        required=['mot_de_passe', 'confirmer_mot_de_passe'],
        properties={
            'mot_de_passe': openapi.Schema(type=openapi.TYPE_STRING),
            'confirmer_mot_de_passe': openapi.Schema(type=openapi.TYPE_STRING),
        }
    ),
    responses={
        200: "Mot de passe modifié",
        400: "Erreur de validation"
    }
)
@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def change_password(request):
    """Changement de mot de passe selon maquette"""
    serializer = ChangePasswordSerializer(data=request.data)
    if serializer.is_valid():
        user = request.user
        user.set_password(serializer.validated_data['mot_de_passe'])
        user.save()
        
        logger.info(f"Mot de passe modifié pour: {user.email}")
        
        return Response({
            'message': 'Mot de passe modifié avec succès'
        })
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class UserProfileView(generics.RetrieveUpdateAPIView):
    """Récupération et mise à jour du profil utilisateur selon maquette"""
    serializer_class = UserProfileSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_object(self):
        return self.request.user
    
    def get_serializer_class(self):
        if self.request.method in ['PUT', 'PATCH']:
            return UserProfileUpdateSerializer
        return UserProfileSerializer

@swagger_auto_schema(
    method='post',
    operation_description="Déconnexion utilisateur",
    request_body=openapi.Schema(
        type=openapi.TYPE_OBJECT,
        properties={
            'refresh': openapi.Schema(type=openapi.TYPE_STRING, description='Refresh token')
        }
    ),
    responses={
        200: "Déconnexion réussie",
        400: "Token invalide"
    }
)
@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def logout(request):
    """Déconnexion utilisateur"""
    try:
        refresh_token = request.data["refresh"]
        token = RefreshToken(refresh_token)
        token.blacklist()
        
        logger.info(f"Déconnexion utilisateur: {request.user.email}")
        
        return Response({'message': 'Déconnexion réussie'})
    except Exception as e:
        return Response({'error': 'Token invalide'}, status=status.HTTP_400_BAD_REQUEST)

def send_verification_code_email(user, code):
    """Envoie l'email avec le code de vérification à 6 chiffres"""
    subject = 'Code de vérification - Cheveux Texturés'
    
    html_message = render_to_string('emails/verification_code.html', {
        'user': user,
        'code': code,
        'app_name': 'Cheveux Texturés'
    })
    plain_message = strip_tags(html_message)
    
    send_mail(
        subject,
        plain_message,
        settings.DEFAULT_FROM_EMAIL,
        [user.email],
        html_message=html_message,
        fail_silently=False,
    )

def send_password_reset_email(user, code):
    """Envoie l'email de réinitialisation avec code 6 chiffres"""
    subject = 'Code de réinitialisation - Cheveux Texturés'
    
    html_message = render_to_string('emails/password_reset.html', {
        'user': user,
        'code': code,
        'app_name': 'Cheveux Texturés'
    })
    plain_message = strip_tags(html_message)
    
    send_mail(
        subject,
        plain_message,
        settings.DEFAULT_FROM_EMAIL,
        [user.email],
        html_message=html_message,
        fail_silently=False,
    )
