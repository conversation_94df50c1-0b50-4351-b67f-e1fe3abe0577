import logging
import time
import json
from django.utils.deprecation import MiddlewareMixin
from django.http import JsonResponse

logger = logging.getLogger('api')
error_logger = logging.getLogger('errors')

class LoggingMiddleware(MiddlewareMixin):
    """Middleware pour logger toutes les requêtes API"""
    
    def process_request(self, request):
        request.start_time = time.time()
        
        # Logger les informations de la requête
        if request.path.startswith('/api/'):
            log_data = {
                'method': request.method,
                'path': request.path,
                'user': str(request.user) if hasattr(request, 'user') and request.user.is_authenticated else 'Anonymous',
                'ip': self.get_client_ip(request),
                'user_agent': request.META.get('HTTP_USER_AGENT', ''),
                'timestamp': time.time()
            }
            
            # Logger le body pour POST/PUT/PATCH (sans les mots de passe)
            if request.method in ['POST', 'PUT', 'PATCH']:
                try:
                    body = json.loads(request.body.decode('utf-8'))
                    # Masquer les champs sensibles
                    sensitive_fields = ['password', 'password_confirm', 'old_password', 'new_password']
                    for field in sensitive_fields:
                        if field in body:
                            body[field] = '***MASKED***'
                    log_data['body'] = body
                except:
                    log_data['body'] = 'Unable to parse body'
            
            logger.info(f"API Request: {json.dumps(log_data)}")
    
    def process_response(self, request, response):
        if hasattr(request, 'start_time') and request.path.startswith('/api/'):
            duration = time.time() - request.start_time
            
            log_data = {
                'method': request.method,
                'path': request.path,
                'status_code': response.status_code,
                'duration_ms': round(duration * 1000, 2),
                'user': str(request.user) if hasattr(request, 'user') and request.user.is_authenticated else 'Anonymous',
            }
            
            # Logger les erreurs avec plus de détails
            if response.status_code >= 400:
                try:
                    response_data = json.loads(response.content.decode('utf-8'))
                    log_data['error_details'] = response_data
                except:
                    log_data['error_details'] = 'Unable to parse response'
                
                error_logger.error(f"API Error: {json.dumps(log_data)}")
            else:
                logger.info(f"API Response: {json.dumps(log_data)}")
        
        return response
    
    def process_exception(self, request, exception):
        if request.path.startswith('/api/'):
            log_data = {
                'method': request.method,
                'path': request.path,
                'exception': str(exception),
                'exception_type': type(exception).__name__,
                'user': str(request.user) if hasattr(request, 'user') and request.user.is_authenticated else 'Anonymous',
            }
            
            error_logger.error(f"API Exception: {json.dumps(log_data)}")
        
        return None
    
    def get_client_ip(self, request):
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
