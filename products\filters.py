import django_filters
from .models import Product, Category, Brand

class ProductFilter(django_filters.FilterSet):
    category = django_filters.ModelChoiceFilter(queryset=Category.objects.all())
    brand = django_filters.ModelChoiceFilter(queryset=Brand.objects.all())
    price_min = django_filters.NumberFilter(field_name="price", lookup_expr='gte')
    price_max = django_filters.NumberFilter(field_name="price", lookup_expr='lte')
    hair_type = django_filters.CharFilter(field_name="hair_type_compatibility", lookup_expr='icontains')
    is_featured = django_filters.BooleanFilter()
    
    class Meta:
        model = Product
        fields = ['category', 'brand', 'price_min', 'price_max', 'hair_type', 'is_featured']
