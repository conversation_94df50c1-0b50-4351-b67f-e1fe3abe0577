from rest_framework import serializers
from .models import (
    PointsTransaction, UserPoints, Reward, UserR<PERSON>ard, 
    Challenge, UserChallenge, RewardProgram
)

class PointsTransactionSerializer(serializers.ModelSerializer):
    class Meta:
        model = PointsTransaction
        fields = [
            'id', 'transaction_type', 'action_type', 'points', 
            'description', 'created_at'
        ]

class UserPointsSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserPoints
        fields = [
            'total_points', 'available_points', 'lifetime_points',
            'daily_streak', 'last_login_date', 'created_at'
        ]

class RewardSerializer(serializers.ModelSerializer):
    is_available = serializers.ReadOnlyField()
    can_afford = serializers.SerializerMethodField()
    
    class Meta:
        model = Reward
        fields = [
            'id', 'name', 'description', 'reward_type', 'points_cost',
            'discount_percentage', 'discount_amount', 'image',
            'is_available', 'can_afford', 'valid_from', 'valid_until'
        ]
    
    def get_can_afford(self, obj):
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            user_points = getattr(request.user, 'points', None)
            if user_points:
                return user_points.available_points >= obj.points_cost
        return False

class UserRewardSerializer(serializers.ModelSerializer):
    reward = RewardSerializer(read_only=True)
    
    class Meta:
        model = UserReward
        fields = [
            'id', 'reward', 'status', 'redemption_code',
            'redeemed_at', 'expires_at', 'used_at'
        ]

class ChallengeSerializer(serializers.ModelSerializer):
    user_progress = serializers.SerializerMethodField()
    is_completed = serializers.SerializerMethodField()
    
    class Meta:
        model = Challenge
        fields = [
            'id', 'name', 'description', 'points_reward',
            'required_actions', 'start_date', 'end_date',
            'max_completions_per_user', 'user_progress', 'is_completed'
        ]
    
    def get_user_progress(self, obj):
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            try:
                user_challenge = UserChallenge.objects.get(
                    user=request.user, challenge=obj
                )
                return user_challenge.progress
            except UserChallenge.DoesNotExist:
                return {}
        return {}
    
    def get_is_completed(self, obj):
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            return UserChallenge.objects.filter(
                user=request.user, challenge=obj, is_completed=True
            ).exists()
        return False

class RewardProgramSerializer(serializers.ModelSerializer):
    class Meta:
        model = RewardProgram
        fields = ['id', 'name', 'description', 'start_date', 'end_date']
