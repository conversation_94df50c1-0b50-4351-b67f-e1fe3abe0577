from rest_framework import serializers
from .models import SubscriptionPlan, UserSubscription, PaymentHistory

class SubscriptionPlanSerializer(serializers.ModelSerializer):
    class Meta:
        model = SubscriptionPlan
        fields = [
            'id', 'name', 'plan_type', 'price', 'duration_days',
            'daily_chat_limit', 'monthly_chat_limit', 'voice_chat_enabled',
            'premium_tips_access', 'priority_support', 'advanced_analysis'
        ]

class UserSubscriptionSerializer(serializers.ModelSerializer):
    plan = SubscriptionPlanSerializer(read_only=True)
    is_active = serializers.ReadOnlyField()
    days_remaining = serializers.SerializerMethodField()
    
    class Meta:
        model = UserSubscription
        fields = [
            'id', 'plan', 'status', 'started_at', 'expires_at',
            'is_active', 'days_remaining', 'auto_renew'
        ]
    
    def get_days_remaining(self, obj):
        if obj.expires_at:
            from django.utils import timezone
            remaining = obj.expires_at - timezone.now()
            return max(0, remaining.days)
        return None

class PaymentHistorySerializer(serializers.ModelSerializer):
    class Meta:
        model = PaymentHistory
        fields = [
            'id', 'amount', 'currency', 'status', 'payment_method',
            'created_at', 'completed_at'
        ]
