from django.core.management.base import BaseCommand
from django.utils import timezone
from accounts.models import User
from products.models import Category, Brand, Product
from tips.models import Tip, DailyTip
from subscription.models import SubscriptionPlan
from onboarding.models import OnboardingStep, OnboardingQuestion, QuestionOption
from datetime import timedelta

class Command(BaseCommand):
    help = 'Crée des données d\'exemple pour l\'application'
    
    def handle(self, *args, **options):
        self.stdout.write('Création des données d\'exemple...')
        
        # Créer les plans d'abonnement
        self.create_subscription_plans()
        
        # Créer les catégories
        self.create_categories()
        
        # Créer les marques
        self.create_brands()
        
        # Créer les produits
        self.create_products()
        
        # Créer les astuces
        self.create_tips()
        
        # Créer l'onboarding
        self.create_onboarding()
        
        self.stdout.write(self.style.SUCCESS('Données d\'exemple créées avec succès!'))
    
    def create_subscription_plans(self):
        plans = [
            {
                'name': '<PERSON><PERSON><PERSON>',
                'plan_type': 'FREE_TRIAL',
                'price': 0.00,
                'duration_days': 7,
                'daily_chat_limit': 10,
            },
            {
                'name': 'Premium Mensuel',
                'plan_type': 'MONTHLY',
                'price': 9.99,
                'duration_days': 30,
                'daily_chat_limit': 0,
                'voice_chat_enabled': True,
                'premium_tips_access': True,
            },
            {
                'name': 'Premium Annuel',
                'plan_type': 'YEARLY',
                'price': 99.99,
                'duration_days': 365,
                'daily_chat_limit': 0,
                'voice_chat_enabled': True,
                'premium_tips_access': True,
                'priority_support': True,
            }
        ]
        
        for plan_data in plans:
            SubscriptionPlan.objects.get_or_create(
                plan_type=plan_data['plan_type'],
                defaults=plan_data
            )
    
    def create_categories(self):
        categories = [
            {'name': 'Nettoyage', 'slug': 'nettoyage'},
            {'name': 'Hydratation & soins', 'slug': 'hydratation-soins'},
            {'name': 'Scellants & nutrition', 'slug': 'scellants-nutrition'},
            {'name': 'Définition & coiffage', 'slug': 'definition-coiffage'},
            {'name': 'Protection & entretien', 'slug': 'protection-entretien'},
            {'name': 'Accessoires associés', 'slug': 'accessoires'},
        ]
        
        for cat_data in categories:
            Category.objects.get_or_create(
                slug=cat_data['slug'],
                defaults=cat_data
            )
    
    def create_brands(self):
        brands = [
            {'name': 'Kiara', 'slug': 'kiara'},
            {'name': 'Shea Moisture', 'slug': 'shea-moisture'},
            {'name': 'Cantu', 'slug': 'cantu'},
            {'name': 'Carol\'s Daughter', 'slug': 'carols-daughter'},
            {'name': 'Mielle Organics', 'slug': 'mielle-organics'},
        ]
        
        for brand_data in brands:
            Brand.objects.get_or_create(
                slug=brand_data['slug'],
                defaults=brand_data
            )
    
    def create_products(self):
        hydratation_cat = Category.objects.get(slug='hydratation-soins')
        scellants_cat = Category.objects.get(slug='scellants-nutrition')
        definition_cat = Category.objects.get(slug='definition-coiffage')
        
        kiara_brand = Brand.objects.get(slug='kiara')
        
        products = [
            {
                'name': 'Masque Boucles Sublimes',
                'slug': 'masque-boucles-sublimes-kiara',
                'brand': kiara_brand,
                'category': hydratation_cat,
                'description': 'Soin profond à base d\'aloé vera et de glycérine pour nourrir intensément les cheveux bouclés et crépus.',
                'short_description': 'Soin profond à base d\'aloé vera et de glycérine...',
                'price': 24.99,
                'hair_type_compatibility': '3A-4C',
                'purchase_url': 'https://cheveuxcrepus.fr/masque-boucles-sublimes',
            },
            {
                'name': 'Huile précieuse de Jojoba Bio',
                'slug': 'huile-jojoba-bio',
                'brand': kiara_brand,
                'category': scellants_cat,
                'description': 'Huile pure de jojoba bio qui protège la fibre capillaire et scelle l\'hydratation.',
                'short_description': 'Protège la fibre capillaire et scelle l\'hydratation...',
                'price': 18.99,
                'hair_type_compatibility': 'ALL',
                'purchase_url': 'https://cheveuxcrepus.fr/huile-jojoba-bio',
            },
            {
                'name': 'Crème Boucles Sculptées',
                'slug': 'creme-boucles-sculptees',
                'brand': kiara_brand,
                'category': definition_cat,
                'description': 'Redéfinit les boucles sans effet carton. Formule enrichie en beurre de karité.',
                'short_description': 'Redéfinit les boucles sans effet carton. Formu...',
                'price': 16.99,
                'hair_type_compatibility': '2A-4C',
                'purchase_url': 'https://cheveuxcrepus.fr/creme-boucles-sculptees',
            }
        ]
        
        for product_data in products:
            Product.objects.get_or_create(
                slug=product_data['slug'],
                defaults=product_data
            )
    
    def create_tips(self):
        hydratation_cat = Category.objects.get(slug='hydratation-soins')
        
        tips = [
            {
                'title': 'Astuces pour maintenir l\'hydratation',
                'slug': 'astuces-maintenir-hydratation',
                'content': 'Éviter la sécheresse et sceller l\'eau dans la fibre capillaire. Applique ton leave-in sur cheveux humides puis une huile légère pour sceller l\'hydratation.',
                'short_description': 'Éviter la sécheresse et sceller l\'eau dans la fibre capillaire.',
                'category': hydratation_cat,
                'hair_types': '3A,3B,3C,4A,4B,4C',
                'is_featured': True,
            },
            {
                'title': 'Nutrition capillaire',
                'slug': 'nutrition-capillaire',
                'content': 'Inclure des aliments riches en oméga-3, comme le saumon et les noix, pour renforcer la brillance et la santé des cheveux.',
                'short_description': 'Aliments bénéfiques pour vos cheveux',
                'category': hydratation_cat,
                'hair_types': 'ALL',
            }
        ]
        
        for i, tip_data in enumerate(tips):
            tip, created = Tip.objects.get_or_create(
                slug=tip_data['slug'],
                defaults=tip_data
            )

            # Créer une astuce du jour pour des dates différentes
            if created:
                tip_date = timezone.now().date() + timedelta(days=i)
                DailyTip.objects.get_or_create(
                    date=tip_date,
                    defaults={'tip': tip, 'is_active': True}
                )
    
    def create_onboarding(self):
        # Étape 1: Bienvenue
        welcome_step, created = OnboardingStep.objects.get_or_create(
            step_number=1,
            defaults={
                'step_type': 'WELCOME',
                'title': 'Bienvenue dans Cheveux Texturés',
                'description': 'Un espace pensé pour toi, pour mieux comprendre, soigner et aimer tes cheveux texturés.',
                'is_required': True,
            }
        )
        
        # Étape 2: Analyse capillaire
        analysis_step, created = OnboardingStep.objects.get_or_create(
            step_number=2,
            defaults={
                'step_type': 'HAIR_ANALYSIS',
                'title': 'Parlons un peu de tes cheveux',
                'description': 'En quelques questions simples, on définit ensemble ton profil capillaire.',
                'is_required': True,
            }
        )
        
        if created:
            # Question sur la forme des cheveux
            hair_shape_q = OnboardingQuestion.objects.create(
                step=analysis_step,
                question_number=1,
                question_type='SINGLE_CHOICE',
                question_text='Quelle est la forme naturelle de tes cheveux ?',
                is_required=True
            )
            
            options = [
                ('Raides', 'straight'),
                ('Ondulés', 'wavy'),
                ('Bouclés', 'curly'),
                ('Crépus', 'coily')
            ]
            
            for i, (text, value) in enumerate(options):
                QuestionOption.objects.create(
                    question=hair_shape_q,
                    option_text=text,
                    option_value=value,
                    order=i
                )
            
            # Question sur la texture
            texture_q = OnboardingQuestion.objects.create(
                step=analysis_step,
                question_number=2,
                question_type='SINGLE_CHOICE',
                question_text='Comment décrirais-tu la texture de tes cheveux ?',
                is_required=True
            )
            
            texture_options = [
                ('Fins', 'fine'),
                ('Moyens', 'medium'),
                ('Épais', 'thick')
            ]
            
            for i, (text, value) in enumerate(texture_options):
                QuestionOption.objects.create(
                    question=texture_q,
                    option_text=text,
                    option_value=value,
                    order=i
                )
