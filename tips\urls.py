from django.urls import path
from . import views

urlpatterns = [
    path('', views.TipListView.as_view(), name='tip_list'),
    path('featured/', views.FeaturedTipsView.as_view(), name='featured_tips'),
    path('daily/', views.DailyTipsView.as_view(), name='daily_tips'),
    path('personalized/', views.personalized_tips, name='personalized_tips'),
    path('category/<slug:category_slug>/', views.tips_by_category, name='tips_by_category'),
    path('likes/', views.UserTipLikesView.as_view(), name='user_tip_likes'),
    path('favorites/', views.UserTipFavoritesView.as_view(), name='user_tip_favorites'),
    path('<int:tip_id>/like/', views.toggle_tip_like, name='toggle_tip_like'),
    path('<int:tip_id>/favorite/', views.toggle_tip_favorite, name='toggle_tip_favorite'),
    path('<slug:slug>/', views.TipDetailView.as_view(), name='tip_detail'),
]
