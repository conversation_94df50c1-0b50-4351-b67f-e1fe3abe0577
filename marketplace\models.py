from django.db import models
from products.models import Product, Brand, Category

class MarketplaceSync(models.Model):
    """Configuration de synchronisation avec Cheveux Crépus"""
    name = models.CharField(max_length=100, default="Cheveux Crépus")
    api_url = models.URLField()
    api_key = models.CharField(max_length=200, blank=True)
    
    # Configuration sync
    sync_products = models.BooleanField(default=True)
    sync_brands = models.BooleanField(default=True)
    sync_categories = models.BooleanField(default=True)
    
    # Fréquence de synchronisation
    sync_frequency_hours = models.IntegerField(default=24)
    last_sync = models.DateTimeField(null=True, blank=True)
    
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    def __str__(self):
        return self.name

class SyncLog(models.Model):
    STATUS_CHOICES = [
        ('SUCCESS', 'Succès'),
        ('ERROR', 'Erreur'),
        ('PARTIAL', 'Partiel'),
    ]
    
    marketplace = models.ForeignKey(MarketplaceSync, on_delete=models.CASCADE, related_name='sync_logs')
    status = models.CharField(max_length=10, choices=STATUS_CHOICES)
    
    # Statistiques
    products_synced = models.IntegerField(default=0)
    products_created = models.IntegerField(default=0)
    products_updated = models.IntegerField(default=0)
    products_errors = models.IntegerField(default=0)
    
    # Détails
    log_details = models.TextField(blank=True)
    error_message = models.TextField(blank=True)
    
    started_at = models.DateTimeField(auto_now_add=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        ordering = ['-started_at']
    
    def __str__(self):
        return f"Sync {self.marketplace.name} - {self.status} ({self.started_at})"

class ExternalProductMapping(models.Model):
    """Mapping entre produits locaux et externes"""
    local_product = models.OneToOneField(Product, on_delete=models.CASCADE, related_name='external_mapping')
    external_id = models.CharField(max_length=100)
    external_url = models.URLField()
    marketplace = models.ForeignKey(MarketplaceSync, on_delete=models.CASCADE)
    
    # Données de synchronisation
    last_synced = models.DateTimeField(auto_now=True)
    sync_enabled = models.BooleanField(default=True)
    
    class Meta:
        unique_together = ['external_id', 'marketplace']
    
    def __str__(self):
        return f"{self.local_product.name} -> {self.external_id}"
