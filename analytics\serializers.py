from rest_framework import serializers
from .models import UserEvent, DailyStats, ConversionFunnel

class UserEventSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserEvent
        fields = ['event_type', 'event_data', 'session_id', 'device_type', 'app_version']

class DailyStatsSerializer(serializers.ModelSerializer):
    class Meta:
        model = DailyStats
        fields = '__all__'

class ConversionFunnelSerializer(serializers.ModelSerializer):
    conversion_rates = serializers.SerializerMethodField()
    
    class Meta:
        model = ConversionFunnel
        fields = '__all__'
    
    def get_conversion_rates(self, obj):
        if obj.app_opens == 0:
            return {}
        
        return {
            'registration_rate': (obj.registrations / obj.app_opens) * 100,
            'onboarding_rate': (obj.onboarding_completed / obj.registrations) * 100 if obj.registrations > 0 else 0,
            'chat_rate': (obj.first_chat / obj.onboarding_completed) * 100 if obj.onboarding_completed > 0 else 0,
            'trial_rate': (obj.trial_started / obj.first_chat) * 100 if obj.first_chat > 0 else 0,
            'subscription_rate': (obj.subscription_purchased / obj.trial_started) * 100 if obj.trial_started > 0 else 0,
        }
