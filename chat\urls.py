from django.urls import path
from . import views

urlpatterns = [
    path('conversations/', views.ConversationListView.as_view(), name='conversation_list'),
    path('conversations/<uuid:id>/', views.ConversationDetailView.as_view(), name='conversation_detail'),
    path('conversations/<uuid:conversation_id>/messages/', views.MessageListView.as_view(), name='message_list'),
    path('conversations/<uuid:conversation_id>/send/', views.send_message, name='send_message'),
    path('sessions/start/', views.start_chat_session, name='start_chat_session'),
    path('sessions/<uuid:session_id>/end/', views.end_chat_session, name='end_chat_session'),
    path('preferences/', views.UserPreferenceView.as_view(), name='chat_preferences'),
    path('statistics/', views.chat_statistics, name='chat_statistics'),
]
