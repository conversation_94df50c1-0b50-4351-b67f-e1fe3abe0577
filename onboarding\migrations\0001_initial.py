# Generated by Django 4.2.7 on 2025-06-26 07:47

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='OnboardingQuestion',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('question_number', models.IntegerField()),
                ('question_type', models.CharField(choices=[('SINGLE_CHOICE', 'Choix unique'), ('MULTIPLE_CHOICE', 'Choix multiple'), ('TEXT', 'Texte libre'), ('SCALE', 'Échelle'), ('IMAGE_CHOICE', "Choix d'image")], max_length=15)),
                ('question_text', models.TextField()),
                ('help_text', models.TextField(blank=True)),
                ('is_required', models.BooleanField(default=True)),
            ],
            options={
                'ordering': ['question_number'],
            },
        ),
        migrations.CreateModel(
            name='OnboardingStep',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('step_number', models.IntegerField()),
                ('step_type', models.CharField(choices=[('WELCOME', 'Bienvenue'), ('PROFILE', 'Profil'), ('HAIR_ANALYSIS', 'Analyse capillaire'), ('PREFERENCES', 'Préférences'), ('GOALS', 'Objectifs'), ('COMPLETION', 'Finalisation')], max_length=15)),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('image', models.ImageField(blank=True, null=True, upload_to='onboarding/')),
                ('is_required', models.BooleanField(default=True)),
                ('is_active', models.BooleanField(default=True)),
            ],
            options={
                'ordering': ['step_number'],
            },
        ),
        migrations.CreateModel(
            name='UserOnboardingProgress',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('completed_steps', models.JSONField(default=list)),
                ('is_completed', models.BooleanField(default=False)),
                ('started_at', models.DateTimeField(auto_now_add=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('current_step', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='onboarding.onboardingstep')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='onboarding_progress', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='QuestionOption',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('option_text', models.CharField(max_length=200)),
                ('option_value', models.CharField(max_length=100)),
                ('option_image', models.ImageField(blank=True, null=True, upload_to='onboarding/options/')),
                ('order', models.IntegerField(default=0)),
                ('question', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='options', to='onboarding.onboardingquestion')),
            ],
            options={
                'ordering': ['order'],
            },
        ),
        migrations.AddField(
            model_name='onboardingquestion',
            name='step',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='questions', to='onboarding.onboardingstep'),
        ),
        migrations.CreateModel(
            name='HairAnalysisResult',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('hair_type_detected', models.CharField(choices=[('1A', 'Cheveux raides fins'), ('1B', 'Cheveux raides moyens'), ('1C', 'Cheveux raides épais'), ('2A', 'Cheveux ondulés fins'), ('2B', 'Cheveux ondulés moyens'), ('2C', 'Cheveux ondulés épais'), ('3A', 'Cheveux bouclés lâches'), ('3B', 'Cheveux bouclés moyens'), ('3C', 'Cheveux bouclés serrés'), ('4A', 'Cheveux crépus fins'), ('4B', 'Cheveux crépus moyens'), ('4C', 'Cheveux crépus épais')], max_length=2)),
                ('porosity_detected', models.CharField(choices=[('LOW', 'Faible porosité'), ('MEDIUM', 'Porosité moyenne'), ('HIGH', 'Haute porosité')], max_length=10)),
                ('recommended_routine', models.TextField()),
                ('recommended_products', models.JSONField(default=list)),
                ('care_tips', models.TextField()),
                ('hair_type_confidence', models.DecimalField(decimal_places=2, default=0.0, max_digits=3)),
                ('porosity_confidence', models.DecimalField(decimal_places=2, default=0.0, max_digits=3)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='hair_analysis', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='UserOnboardingAnswer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('text_answer', models.TextField(blank=True)),
                ('selected_options', models.JSONField(default=list)),
                ('scale_value', models.IntegerField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('question', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='onboarding.onboardingquestion')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='onboarding_answers', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'unique_together': {('user', 'question')},
            },
        ),
        migrations.AlterUniqueTogether(
            name='onboardingquestion',
            unique_together={('step', 'question_number')},
        ),
    ]
