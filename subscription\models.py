from django.db import models
from accounts.models import User
from django.utils import timezone
from datetime import timedelta

class SubscriptionPlan(models.Model):
    PLAN_TYPES = [
        ('FREE_TRIAL', 'Essai gratuit'),
        ('MONTHLY', 'Mensuel'),
        ('YEARLY', 'Annuel'),
        ('LIFETIME', 'Vie'),
    ]
    
    name = models.CharField(max_length=100)
    plan_type = models.CharField(max_length=15, choices=PLAN_TYPES, unique=True)
    price = models.DecimalField(max_digits=10, decimal_places=2)
    duration_days = models.IntegerField(help_text="Durée en jours, 0 pour illimité")
    
    # Limites du plan
    daily_chat_limit = models.IntegerField(default=0, help_text="0 = illimité")
    monthly_chat_limit = models.IntegerField(default=0, help_text="0 = illimité")
    
    # Fonctionnalités
    voice_chat_enabled = models.BooleanField(default=True)
    premium_tips_access = models.BooleanField(default=False)
    priority_support = models.BooleanField(default=False)
    advanced_analysis = models.BooleanField(default=False)
    
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    def __str__(self):
        return f"{self.name} - {self.price}€"

class UserSubscription(models.Model):
    STATUS_CHOICES = [
        ('ACTIVE', 'Actif'),
        ('EXPIRED', 'Expiré'),
        ('CANCELLED', 'Annulé'),
        ('PENDING', 'En attente'),
    ]
    
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='subscription')
    plan = models.ForeignKey(SubscriptionPlan, on_delete=models.CASCADE)
    status = models.CharField(max_length=10, choices=STATUS_CHOICES, default='PENDING')
    
    started_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField(null=True, blank=True)
    cancelled_at = models.DateTimeField(null=True, blank=True)
    
    # Paiement
    payment_method = models.CharField(max_length=50, blank=True)
    transaction_id = models.CharField(max_length=100, blank=True)
    
    # Auto-renouvellement
    auto_renew = models.BooleanField(default=True)
    
    def save(self, *args, **kwargs):
        if not self.expires_at and self.plan.duration_days > 0:
            self.expires_at = self.started_at + timedelta(days=self.plan.duration_days)
        super().save(*args, **kwargs)
    
    @property
    def is_active(self):
        if self.status != 'ACTIVE':
            return False
        if self.expires_at and timezone.now() > self.expires_at:
            return False
        return True
    
    def __str__(self):
        return f"{self.user.full_name} - {self.plan.name}"

class PaymentHistory(models.Model):
    PAYMENT_STATUS = [
        ('PENDING', 'En attente'),
        ('COMPLETED', 'Complété'),
        ('FAILED', 'Échoué'),
        ('REFUNDED', 'Remboursé'),
    ]
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='payments')
    subscription = models.ForeignKey(UserSubscription, on_delete=models.CASCADE, related_name='payments')
    
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    currency = models.CharField(max_length=3, default='EUR')
    status = models.CharField(max_length=10, choices=PAYMENT_STATUS, default='PENDING')
    
    payment_method = models.CharField(max_length=50)
    transaction_id = models.CharField(max_length=100, unique=True)
    gateway_response = models.JSONField(default=dict)
    
    created_at = models.DateTimeField(auto_now_add=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    
    def __str__(self):
        return f"{self.user.full_name} - {self.amount}€ ({self.status})"
