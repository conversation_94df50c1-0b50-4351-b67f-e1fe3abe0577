from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from .models import User, PasswordResetCode

@admin.register(User)
class CustomUserAdmin(UserAdmin):
    list_display = ('email', 'first_name', 'last_name', 'hair_type', 'is_premium', 'onboarding_completed')
    list_filter = ('hair_type', 'hair_porosity', 'is_premium', 'onboarding_completed')
    search_fields = ('email', 'first_name', 'last_name')
    
    fieldsets = UserAdmin.fieldsets + (
        ('Profil Capillaire', {
            'fields': ('hair_type', 'hair_porosity', 'hair_length', 'hair_concerns', 'current_routine')
        }),
        ('Préférences', {
            'fields': ('preferred_brands', 'budget_range')
        }),
        ('Abonnement', {
            'fields': ('is_premium', 'premium_expires_at', 'trial_used')
        }),
        ('Onboarding', {
            'fields': ('onboarding_completed',)
        }),
    )

@admin.register(PasswordResetCode)
class PasswordResetCodeAdmin(admin.ModelAdmin):
    list_display = ('user', 'code', 'created_at', 'is_used')
    list_filter = ('is_used', 'created_at')
    search_fields = ('user__email', 'code')
