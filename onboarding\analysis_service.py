from .models import UserOnboardingAnswer, HairAnalysisResult
from accounts.models import User
from products.models import Product
import json

class HairAnalysisService:
    """Service d'analyse capillaire basé sur les réponses d'onboarding"""
    
    def analyze_user_hair(self, user):
        """Analyse le type de cheveux et la porosité basé sur les réponses"""
        answers = UserOnboardingAnswer.objects.filter(user=user)
        
        # Analyser le type de cheveux
        hair_type, hair_type_confidence = self._analyze_hair_type(answers)
        
        # Analyser la porosité
        porosity, porosity_confidence = self._analyze_porosity(answers)
        
        # Générer des recommandations
        routine = self._generate_routine(hair_type, porosity)
        products = self._recommend_products(hair_type, porosity)
        tips = self._generate_care_tips(hair_type, porosity)
        
        # C<PERSON>er ou mettre à jour l'analyse
        analysis, created = HairAnalysisResult.objects.get_or_create(
            user=user,
            defaults={
                'hair_type_detected': hair_type,
                'porosity_detected': porosity,
                'recommended_routine': routine,
                'recommended_products': products,
                'care_tips': tips,
                'hair_type_confidence': hair_type_confidence,
                'porosity_confidence': porosity_confidence
            }
        )
        
        if not created:
            analysis.hair_type_detected = hair_type
            analysis.porosity_detected = porosity
            analysis.recommended_routine = routine
            analysis.recommended_products = products
            analysis.care_tips = tips
            analysis.hair_type_confidence = hair_type_confidence
            analysis.porosity_confidence = porosity_confidence
            analysis.save()
        
        # Mettre à jour le profil utilisateur
        user.hair_type = hair_type
        user.hair_porosity = porosity
        user.save()
        
        return analysis
    
    def _analyze_hair_type(self, answers):
        """Détermine le type de cheveux basé sur les réponses"""
        # Logique simplifiée - à améliorer avec de vraies règles
        hair_type_scores = {
            '1A': 0, '1B': 0, '1C': 0,
            '2A': 0, '2B': 0, '2C': 0,
            '3A': 0, '3B': 0, '3C': 0,
            '4A': 0, '4B': 0, '4C': 0,
        }
        
        for answer in answers:
            question_text = answer.question.question_text.lower()
            
            # Analyse basée sur les mots-clés dans les questions
            if 'forme' in question_text or 'boucle' in question_text:
                if 'raide' in str(answer.selected_options).lower():
                    hair_type_scores['1B'] += 3
                elif 'ondulé' in str(answer.selected_options).lower():
                    hair_type_scores['2B'] += 3
                elif 'bouclé' in str(answer.selected_options).lower():
                    hair_type_scores['3B'] += 3
                elif 'crépu' in str(answer.selected_options).lower():
                    hair_type_scores['4B'] += 3
            
            if 'texture' in question_text:
                if 'fin' in str(answer.selected_options).lower():
                    for key in hair_type_scores:
                        if key.endswith('A'):
                            hair_type_scores[key] += 1
                elif 'épais' in str(answer.selected_options).lower():
                    for key in hair_type_scores:
                        if key.endswith('C'):
                            hair_type_scores[key] += 1
        
        # Trouver le type avec le score le plus élevé
        best_type = max(hair_type_scores, key=hair_type_scores.get)
        confidence = min(hair_type_scores[best_type] / 10.0, 1.0)  # Normaliser sur 1.0
        
        return best_type, confidence
    
    def _analyze_porosity(self, answers):
        """Détermine la porosité basée sur les réponses"""
        porosity_scores = {'LOW': 0, 'MEDIUM': 0, 'HIGH': 0}
        
        for answer in answers:
            question_text = answer.question.question_text.lower()
            
            if 'sèche' in question_text or 'hydratation' in question_text:
                if 'rapidement' in str(answer.selected_options).lower():
                    porosity_scores['HIGH'] += 2
                elif 'lentement' in str(answer.selected_options).lower():
                    porosity_scores['LOW'] += 2
                else:
                    porosity_scores['MEDIUM'] += 1
        
        best_porosity = max(porosity_scores, key=porosity_scores.get)
        confidence = min(porosity_scores[best_porosity] / 5.0, 1.0)
        
        return best_porosity, confidence
    
    def _generate_routine(self, hair_type, porosity):
        """Génère une routine personnalisée"""
        routines = {
            ('4A', 'HIGH'): """
            Routine pour cheveux crépus à haute porosité:
            1. Pré-shampoing avec une huile (coco, olive)
            2. Shampoing doux sans sulfates
            3. Masque hydratant profond (15-20 min)
            4. Leave-in hydratant
            5. Sceller avec une huile légère
            Fréquence: 1-2 fois par semaine
            """,
            ('3B', 'MEDIUM'): """
            Routine pour cheveux bouclés à porosité moyenne:
            1. Co-wash ou shampoing doux
            2. Après-shampoing démêlant
            3. Leave-in crème
            4. Gel ou crème coiffante
            5. Scrunching pour définir les boucles
            Fréquence: 2-3 fois par semaine
            """
        }
        
        return routines.get((hair_type, porosity), 
            "Routine personnalisée en cours de génération...")
    
    def _recommend_products(self, hair_type, porosity):
        """Recommande des produits basés sur l'analyse"""
        # Récupérer des produits compatibles
        compatible_products = Product.objects.filter(
            is_active=True,
            hair_type_compatibility__in=['ALL', f'{hair_type[0]}A-{hair_type[0]}C']
        )[:5]
        
        return [
            {
                'id': product.id,
                'name': product.name,
                'brand': product.brand.name,
                'category': product.category.name,
                'reason': f'Adapté aux cheveux {hair_type} avec porosité {porosity.lower()}'
            }
            for product in compatible_products
        ]
    
    def _generate_care_tips(self, hair_type, porosity):
        """Génère des conseils de soin personnalisés"""
        tips = {
            'HIGH': [
                "Utilisez des protéines légères pour renforcer vos cheveux",
                "Scellez toujours l'hydratation avec une huile",
                "Évitez les produits trop lourds qui peuvent alourdir"
            ],
            'LOW': [
                "Utilisez de la chaleur pour aider les produits à pénétrer",
                "Privilégiez les huiles légères comme le jojoba",
                "Évitez les protéines qui peuvent durcir vos cheveux"
            ],
            'MEDIUM': [
                "Équilibrez hydratation et protéines",
                "Variez vos soins selon les besoins",
                "Écoutez vos cheveux et adaptez votre routine"
            ]
        }
        
        return "\n".join(tips.get(porosity, ["Conseils personnalisés à venir..."]))
