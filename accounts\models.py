from django.contrib.auth.models import AbstractUser
from django.db import models
from django.utils import timezone
import random
import string

class User(AbstractUser):
    HAIR_TYPES = [
        ('1A', 'Cheveux raides fins'),
        ('1B', 'Cheveux raides moyens'),
        ('1C', 'Cheveux raides épais'),
        ('2A', 'Cheveux ondulés fins'),
        ('2B', 'Cheveux ondulés moyens'),
        ('2C', 'Cheveux ondulés épais'),
        ('3A', 'Cheveux bouclés lâches'),
        ('3B', 'Cheveux bouclés moyens'),
        ('3C', 'Cheveux bouclés serrés'),
        ('4A', 'Cheveux crépus fins'),
        ('4B', 'Cheveux crépus moyens'),
        ('4C', 'Cheveux crépus épais'),
    ]
    
    POROSITY_CHOICES = [
        ('LOW', 'Faible porosité'),
        ('MEDIUM', '<PERSON><PERSON><PERSON> moyenne'),
        ('HIGH', 'Haute porosité'),
    ]
    
    # Champs de base selon les maquettes
    email = models.EmailField(unique=True)
    nom = models.CharField(max_length=50)  # Selon maquette inscription
    mail = models.EmailField()  # Champ mail séparé selon maquette
    mot_de_passe = models.CharField(max_length=128)  # Selon maquette
    
    # Profil utilisateur selon maquettes
    phone = models.CharField(max_length=15, blank=True)
    profile_picture = models.ImageField(upload_to='profiles/', blank=True, null=True)
    
    # Profil capillaire détaillé selon les maquettes
    hair_type = models.CharField(max_length=2, choices=HAIR_TYPES, blank=True)
    hair_porosity = models.CharField(max_length=10, choices=POROSITY_CHOICES, blank=True)
    hair_length = models.CharField(max_length=50, blank=True)
    hair_concerns = models.TextField(blank=True)
    current_routine = models.TextField(blank=True)
    preferred_brands = models.TextField(blank=True)
    budget_range = models.CharField(max_length=50, blank=True)
    
    # Abonnement et essai
    is_premium = models.BooleanField(default=False)
    premium_expires_at = models.DateTimeField(null=True, blank=True)
    trial_used = models.BooleanField(default=False)
    trial_started_at = models.DateTimeField(null=True, blank=True)
    trial_duration_days = models.IntegerField(default=7)
    subscription_type = models.CharField(
        max_length=20,
        choices=[
            ('FREE_TRIAL', 'Essai gratuit'),
            ('MONTHLY', 'Mensuel'),
            ('YEARLY', 'Annuel'),
            ('LIFETIME', 'Vie'),
        ],
        default='FREE_TRIAL'
    )
    
    # RGPD et consentements
    gdpr_consent = models.BooleanField(default=False)
    marketing_consent = models.BooleanField(default=False)
    data_processing_consent = models.BooleanField(default=False)
    consent_date = models.DateTimeField(null=True, blank=True)
    
    # Limitations chat
    daily_chat_limit = models.IntegerField(default=10)
    daily_chat_count = models.IntegerField(default=0)
    last_chat_reset = models.DateField(null=True, blank=True)
    
    # Vérification email avec CODE 6 CHIFFRES (selon maquette)
    email_verified = models.BooleanField(default=False)
    email_verification_code = models.CharField(max_length=6, blank=True)  # CODE 6 CHIFFRES
    verification_code_expires = models.DateTimeField(null=True, blank=True)
    
    # Onboarding
    onboarding_completed = models.BooleanField(default=False)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['username', 'nom']
    
    def __str__(self):
        return f"{self.nom}"
    
    @property
    def full_name(self):
        return self.nom
    
    @property
    def is_trial_expired(self):
        if not self.trial_started_at:
            return False
        from datetime import timedelta
        return timezone.now() > self.trial_started_at + timedelta(days=self.trial_duration_days)
    
    @property
    def can_chat(self):
        if self.is_premium:
            return True
        today = timezone.now().date()
        if self.last_chat_reset != today:
            self.daily_chat_count = 0
            self.last_chat_reset = today
            self.save()
        return self.daily_chat_count < self.daily_chat_limit
    
    def increment_chat_count(self):
        if not self.is_premium:
            self.daily_chat_count += 1
            self.save()
    
    def generate_email_verification_code(self):
        """Génère un code de vérification à 6 chiffres"""
        from datetime import timedelta
        self.email_verification_code = ''.join(random.choices(string.digits, k=6))
        self.verification_code_expires = timezone.now() + timedelta(minutes=10)
        self.save()
        return self.email_verification_code
    
    def is_verification_code_valid(self, code):
        """Vérifie si le code est valide"""
        if not self.email_verification_code or not self.verification_code_expires:
            return False
        if timezone.now() > self.verification_code_expires:
            return False
        return self.email_verification_code == code

class PasswordResetCode(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    code = models.CharField(max_length=6)  # CODE 6 CHIFFRES selon maquette
    created_at = models.DateTimeField(auto_now_add=True)
    is_used = models.BooleanField(default=False)
    expires_at = models.DateTimeField()
    
    def save(self, *args, **kwargs):
        if not self.code:
            self.code = ''.join(random.choices(string.digits, k=6))
        if not self.expires_at:
            from datetime import timedelta
            self.expires_at = timezone.now() + timedelta(minutes=10)
        super().save(*args, **kwargs)
    
    def is_valid(self):
        return (
            not self.is_used and 
            timezone.now() < self.expires_at
        )
    
    def __str__(self):
        return f"Code {self.code} for {self.user.email}"
