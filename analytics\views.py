from rest_framework import generics, status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django.utils import timezone
from django.db.models import Count, Sum
from datetime import timedelta
from .models import UserEvent, DailyStats, ConversionFunnel
from .serializers import UserEventSerializer, DailyStatsSerializer, ConversionFunnelSerializer
from accounts.models import User

@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def track_event(request):
    """Enregistrer un événement utilisateur"""
    serializer = UserEventSerializer(data=request.data)
    if serializer.is_valid():
        serializer.save(user=request.user)
        return Response({'message': 'Événement enregistré'})
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

@api_view(['GET'])
@permission_classes([permissions.IsAdminUser])
def dashboard_stats(request):
    """Statistiques pour le dashboard admin"""
    today = timezone.now().date()
    yesterday = today - timedelta(days=1)
    last_week = today - timedelta(days=7)
    last_month = today - timedelta(days=30)
    
    # Statistiques utilisateurs
    total_users = User.objects.count()
    new_users_today = User.objects.filter(date_joined__date=today).count()
    premium_users = User.objects.filter(is_premium=True).count()
    
    # Statistiques d'engagement
    active_users_today = UserEvent.objects.filter(
        created_at__date=today
    ).values('user').distinct().count()
    
    chat_messages_today = UserEvent.objects.filter(
        event_type='CHAT_MESSAGE',
        created_at__date=today
    ).count()
    
    # Revenus
    from subscription.models import PaymentHistory
    revenue_today = PaymentHistory.objects.filter(
        created_at__date=today,
        status='COMPLETED'
    ).aggregate(total=Sum('amount'))['total'] or 0
    
    return Response({
        'users': {
            'total': total_users,
            'new_today': new_users_today,
            'premium': premium_users,
            'active_today': active_users_today,
        },
        'engagement': {
            'chat_messages_today': chat_messages_today,
        },
        'revenue': {
            'today': float(revenue_today),
        }
    })

@api_view(['GET'])
@permission_classes([permissions.IsAdminUser])
def user_analytics(request):
    """Analytics détaillées des utilisateurs"""
    days = int(request.GET.get('days', 30))
    start_date = timezone.now().date() - timedelta(days=days)
    
    # Évolution des utilisateurs
    user_growth = []
    for i in range(days):
        date = start_date + timedelta(days=i)
        new_users = User.objects.filter(date_joined__date=date).count()
        total_users = User.objects.filter(date_joined__date__lte=date).count()
        user_growth.append({
            'date': date,
            'new_users': new_users,
            'total_users': total_users
        })
    
    # Top événements
    top_events = UserEvent.objects.filter(
        created_at__date__gte=start_date
    ).values('event_type').annotate(
        count=Count('id')
    ).order_by('-count')[:10]
    
    return Response({
        'user_growth': user_growth,
        'top_events': list(top_events),
    })

class DailyStatsView(generics.ListAPIView):
    serializer_class = DailyStatsSerializer
    permission_classes = [permissions.IsAdminUser]
    
    def get_queryset(self):
        days = int(self.request.GET.get('days', 30))
        start_date = timezone.now().date() - timedelta(days=days)
        return DailyStats.objects.filter(date__gte=start_date)

class ConversionFunnelView(generics.ListAPIView):
    serializer_class = ConversionFunnelSerializer
    permission_classes = [permissions.IsAdminUser]
    
    def get_queryset(self):
        days = int(self.request.GET.get('days', 30))
        start_date = timezone.now().date() - timedelta(days=days)
        return ConversionFunnel.objects.filter(date__gte=start_date)
