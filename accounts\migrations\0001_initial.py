# Generated by Django 4.2.7 on 2025-06-26 07:47

from django.conf import settings
import django.contrib.auth.models
import django.contrib.auth.validators
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='User',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('username', models.Char<PERSON>ield(error_messages={'unique': 'A user with that username already exists.'}, help_text='Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.', max_length=150, unique=True, validators=[django.contrib.auth.validators.UnicodeUsernameValidator()], verbose_name='username')),
                ('first_name', models.CharField(blank=True, max_length=150, verbose_name='first name')),
                ('last_name', models.CharField(blank=True, max_length=150, verbose_name='last name')),
                ('is_staff', models.BooleanField(default=False, help_text='Designates whether the user can log into this admin site.', verbose_name='staff status')),
                ('is_active', models.BooleanField(default=True, help_text='Designates whether this user should be treated as active. Unselect this instead of deleting accounts.', verbose_name='active')),
                ('date_joined', models.DateTimeField(default=django.utils.timezone.now, verbose_name='date joined')),
                ('email', models.EmailField(max_length=254, unique=True)),
                ('nom', models.CharField(max_length=50)),
                ('mail', models.EmailField(max_length=254)),
                ('mot_de_passe', models.CharField(max_length=128)),
                ('phone', models.CharField(blank=True, max_length=15)),
                ('profile_picture', models.ImageField(blank=True, null=True, upload_to='profiles/')),
                ('hair_type', models.CharField(blank=True, choices=[('1A', 'Cheveux raides fins'), ('1B', 'Cheveux raides moyens'), ('1C', 'Cheveux raides épais'), ('2A', 'Cheveux ondulés fins'), ('2B', 'Cheveux ondulés moyens'), ('2C', 'Cheveux ondulés épais'), ('3A', 'Cheveux bouclés lâches'), ('3B', 'Cheveux bouclés moyens'), ('3C', 'Cheveux bouclés serrés'), ('4A', 'Cheveux crépus fins'), ('4B', 'Cheveux crépus moyens'), ('4C', 'Cheveux crépus épais')], max_length=2)),
                ('hair_porosity', models.CharField(blank=True, choices=[('LOW', 'Faible porosité'), ('MEDIUM', 'Porosité moyenne'), ('HIGH', 'Haute porosité')], max_length=10)),
                ('hair_length', models.CharField(blank=True, max_length=50)),
                ('hair_concerns', models.TextField(blank=True)),
                ('current_routine', models.TextField(blank=True)),
                ('preferred_brands', models.TextField(blank=True)),
                ('budget_range', models.CharField(blank=True, max_length=50)),
                ('is_premium', models.BooleanField(default=False)),
                ('premium_expires_at', models.DateTimeField(blank=True, null=True)),
                ('trial_used', models.BooleanField(default=False)),
                ('trial_started_at', models.DateTimeField(blank=True, null=True)),
                ('trial_duration_days', models.IntegerField(default=7)),
                ('subscription_type', models.CharField(choices=[('FREE_TRIAL', 'Essai gratuit'), ('MONTHLY', 'Mensuel'), ('YEARLY', 'Annuel'), ('LIFETIME', 'Vie')], default='FREE_TRIAL', max_length=20)),
                ('gdpr_consent', models.BooleanField(default=False)),
                ('marketing_consent', models.BooleanField(default=False)),
                ('data_processing_consent', models.BooleanField(default=False)),
                ('consent_date', models.DateTimeField(blank=True, null=True)),
                ('daily_chat_limit', models.IntegerField(default=10)),
                ('daily_chat_count', models.IntegerField(default=0)),
                ('last_chat_reset', models.DateField(blank=True, null=True)),
                ('email_verified', models.BooleanField(default=False)),
                ('email_verification_code', models.CharField(blank=True, max_length=6)),
                ('verification_code_expires', models.DateTimeField(blank=True, null=True)),
                ('onboarding_completed', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('groups', models.ManyToManyField(blank=True, help_text='The groups this user belongs to. A user will get all permissions granted to each of their groups.', related_name='user_set', related_query_name='user', to='auth.group', verbose_name='groups')),
                ('user_permissions', models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='user_set', related_query_name='user', to='auth.permission', verbose_name='user permissions')),
            ],
            options={
                'verbose_name': 'user',
                'verbose_name_plural': 'users',
                'abstract': False,
            },
            managers=[
                ('objects', django.contrib.auth.models.UserManager()),
            ],
        ),
        migrations.CreateModel(
            name='PasswordResetCode',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=6)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('is_used', models.BooleanField(default=False)),
                ('expires_at', models.DateTimeField()),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
