from django.urls import path
from . import views

urlpatterns = [
    path('points/', views.UserPointsView.as_view(), name='user_points'),
    path('points/history/', views.PointsHistoryView.as_view(), name='points_history'),
    path('points/checkin/', views.daily_checkin, name='daily_checkin'),
    path('points/award/', views.award_points, name='award_points'),
    path('rewards/', views.RewardListView.as_view(), name='reward_list'),
    path('rewards/<int:reward_id>/redeem/', views.redeem_reward, name='redeem_reward'),
    path('my-rewards/', views.UserRewardsView.as_view(), name='user_rewards'),
    path('challenges/', views.ChallengeListView.as_view(), name='challenge_list'),
    path('leaderboard/', views.leaderboard, name='leaderboard'),
]
