from django.contrib import admin
from .models import Tip, TipLike, TipFavorite, DailyTip, TipStep

class TipStepInline(admin.TabularInline):
    model = TipStep
    extra = 1

@admin.register(Tip)
class TipAdmin(admin.ModelAdmin):
    list_display = ('title', 'category', 'difficulty', 'is_featured', 'views_count', 'likes_count', 'is_active')
    list_filter = ('category', 'difficulty', 'is_featured', 'is_active', 'created_at')
    search_fields = ('title', 'content', 'tags')
    prepopulated_fields = {'slug': ('title',)}
    list_editable = ('is_featured', 'is_active')
    inlines = [TipStepInline]
    
    fieldsets = (
        ('Contenu', {
            'fields': ('title', 'slug', 'short_description', 'content', 'category')
        }),
        ('Médias', {
            'fields': ('featured_image', 'video_url')
        }),
        ('Paramètres', {
            'fields': ('difficulty', 'hair_types', 'is_featured', 'is_active')
        }),
        ('SEO', {
            'fields': ('meta_description', 'tags'),
            'classes': ('collapse',)
        }),
        ('Statistiques', {
            'fields': ('views_count', 'likes_count'),
            'classes': ('collapse',)
        }),
    )

@admin.register(DailyTip)
class DailyTipAdmin(admin.ModelAdmin):
    list_display = ('date', 'tip', 'is_active')
    list_filter = ('date', 'is_active')
    search_fields = ('tip__title',)

@admin.register(TipLike)
class TipLikeAdmin(admin.ModelAdmin):
    list_display = ('user', 'tip', 'created_at')
    list_filter = ('created_at',)
    search_fields = ('user__email', 'tip__title')

@admin.register(TipFavorite)
class TipFavoriteAdmin(admin.ModelAdmin):
    list_display = ('user', 'tip', 'created_at')
    list_filter = ('created_at',)
    search_fields = ('user__email', 'tip__title')
