import openai
from django.conf import settings
from typing import List, Dict, Any
import time
import json
import logging
from products.models import Product

logger = logging.getLogger('api')

class AIService:
    def __init__(self):
        openai.api_key = settings.OPENAI_API_KEY
        self.model = "gpt-3.5-turbo"
        self.max_tokens = 500
        
    def generate_response(self, user_message: str, user_profile, conversation_history) -> Dict[str, Any]:
        """Génère une réponse IA personnalisée avec recommandations produits"""
        start_time = time.time()
        
        # Construire le contexte utilisateur
        user_context = self._build_user_context(user_profile)
        
        # Construire l'historique de conversation
        history_context = self._build_conversation_history(conversation_history)
        
        # Récupérer des produits pertinents
        relevant_products = self._get_relevant_products(user_message, user_profile)
        
        # Prompt système spécialisé pour les cheveux texturés
        system_prompt = f"""Tu es un coach capillaire expert spécialis<PERSON> dans les cheveux texturés, bouclés et crépus. 
        Tu t'appelles l'Assistant Cheveux Texturés et tu es là pour aider {user_profile.first_name}.

        Contexte utilisateur:
        {user_context}

        Produits disponibles pour recommandations:
        {self._format_products_for_ai(relevant_products)}

        Tes responsabilités:
        - Donner des conseils personnalisés sur les soins capillaires
        - Recommander des produits adaptés au type de cheveux (utilise les produits fournis)
        - Expliquer les techniques de coiffage
        - Aider à résoudre les problèmes capillaires
        - Être empathique et encourageant
        - Quand tu recommandes un produit, utilise le format: [PRODUIT:ID:nom_du_produit]

        Style de communication:
        - Amical et bienveillant
        - Utilise le prénom de l'utilisateur
        - Donne des conseils pratiques et précis
        - Pose des questions pour mieux comprendre les besoins
        - Utilise des emojis pour rendre la conversation plus chaleureuse
        """
        
        messages = [
            {"role": "system", "content": system_prompt},
            *history_context,
            {"role": "user", "content": user_message}
        ]
        
        try:
            response = openai.ChatCompletion.create(
                model=self.model,
                messages=messages,
                max_tokens=self.max_tokens,
                temperature=0.7,
                frequency_penalty=0.1,
                presence_penalty=0.1
            )
            
            processing_time = time.time() - start_time
            ai_response = response.choices[0].message.content
            
            # Extraire les recommandations produits
            recommended_products = self._extract_product_recommendations(ai_response)
            
            logger.info(f"Réponse IA générée pour {user_profile.email} en {processing_time:.2f}s")
            
            return {
                'content': ai_response,
                'model': self.model,
                'tokens_used': response.usage.total_tokens,
                'processing_time': processing_time,
                'recommended_products': recommended_products
            }
            
        except Exception as e:
            logger.error(f"Erreur génération IA: {e}")
            # Réponse de fallback en cas d'erreur
            return {
                'content': f"Désolé {user_profile.first_name}, je rencontre un problème technique. Peux-tu reformuler ta question ? 😊",
                'model': 'fallback',
                'tokens_used': 0,
                'processing_time': time.time() - start_time,
                'error': str(e),
                'recommended_products': []
            }
    
    def _build_user_context(self, user_profile) -> str:
        """Construit le contexte utilisateur pour l'IA"""
        context_parts = [
            f"Nom: {user_profile.first_name} {user_profile.last_name}"
        ]
        
        if user_profile.hair_type:
            context_parts.append(f"Type de cheveux: {user_profile.get_hair_type_display()}")
        
        if user_profile.hair_porosity:
            context_parts.append(f"Porosité: {user_profile.get_hair_porosity_display()}")
        
        if user_profile.hair_length:
            context_parts.append(f"Longueur: {user_profile.hair_length}")
        
        if user_profile.hair_concerns:
            context_parts.append(f"Préoccupations: {user_profile.hair_concerns}")
        
        if user_profile.current_routine:
            context_parts.append(f"Routine actuelle: {user_profile.current_routine}")
        
        if user_profile.preferred_brands:
            context_parts.append(f"Marques préférées: {user_profile.preferred_brands}")
        
        return "\n".join(context_parts)
    
    def _build_conversation_history(self, messages) -> List[Dict[str, str]]:
        """Construit l'historique de conversation pour l'IA"""
        history = []
        
        for message in messages.order_by('created_at'):
            role = "user" if message.sender_type == "USER" else "assistant"
            history.append({
                "role": role,
                "content": message.content
            })
        
        return history[-10:]  # Garde seulement les 10 derniers messages
    
    def _get_relevant_products(self, user_message: str, user_profile) -> List[Product]:
        """Récupère les produits pertinents pour la conversation"""
        # Mots-clés pour différents types de produits
        keywords_mapping = {
            'hydratation': ['hydrat', 'sec', 'sécheresse', 'moisture', 'leave-in'],
            'nettoyage': ['shampoing', 'laver', 'nettoyer', 'cleanse'],
            'nutrition': ['nutrition', 'nourrir', 'huile', 'beurre', 'masque'],
            'coiffage': ['coiffer', 'définir', 'boucles', 'gel', 'crème'],
            'protection': ['protéger', 'chaleur', 'soleil', 'protection']
        }
        
        # Analyser le message pour déterminer les catégories pertinentes
        relevant_categories = []
        user_message_lower = user_message.lower()
        
        for category, keywords in keywords_mapping.items():
            if any(keyword in user_message_lower for keyword in keywords):
                relevant_categories.append(category)
        
        # Si aucune catégorie spécifique, utiliser le profil utilisateur
        if not relevant_categories:
            if user_profile.hair_type and user_profile.hair_type.startswith(('3', '4')):
                relevant_categories = ['hydratation', 'nutrition']
        
        # Récupérer les produits
        products = Product.objects.filter(is_active=True)
        
        # Filtrer par compatibilité cheveux
        if user_profile.hair_type:
            if user_profile.hair_type.startswith('3') or user_profile.hair_type.startswith('4'):
                products = products.filter(
                    hair_type_compatibility__in=['ALL', '3A-4C', '3A-3C', '4A-4C']
                )
        
        # Filtrer par catégories pertinentes si trouvées
        if relevant_categories:
            category_filters = []
            for cat in relevant_categories:
                if cat == 'hydratation':
                    category_filters.append('hydratation-soins')
                elif cat == 'nutrition':
                    category_filters.append('scellants-nutrition')
                elif cat == 'coiffage':
                    category_filters.append('definition-coiffage')
                elif cat == 'nettoyage':
                    category_filters.append('nettoyage')
                elif cat == 'protection':
                    category_filters.append('protection-entretien')
            
            if category_filters:
                products = products.filter(category__slug__in=category_filters)
        
        return products[:5]  # Limiter à 5 produits
    
    def _format_products_for_ai(self, products: List[Product]) -> str:
        """Formate les produits pour l'IA"""
        if not products:
            return "Aucun produit spécifique disponible."
        
        formatted_products = []
        for product in products:
            formatted_products.append(
                f"ID:{product.id} - {product.name} par {product.brand.name} "
                f"({product.category.name}) - {product.short_description} - {product.price}€"
            )
        
        return "\n".join(formatted_products)
    
    def _extract_product_recommendations(self, ai_response: str) -> List[Dict]:
        """Extrait les recommandations produits de la réponse IA"""
        import re
        
        # Pattern pour extraire [PRODUIT:ID:nom]
        pattern = r'\[PRODUIT:(\d+):([^\]]+)\]'
        matches = re.findall(pattern, ai_response)
        
        recommendations = []
        for product_id, product_name in matches:
            try:
                product = Product.objects.get(id=int(product_id))
                recommendations.append({
                    'id': product.id,
                    'name': product.name,
                    'brand': product.brand.name,
                    'price': float(product.price),
                    'image': product.main_image.url if product.main_image else None,
                    'url': product.purchase_url
                })
            except Product.DoesNotExist:
                continue
        
        return recommendations
