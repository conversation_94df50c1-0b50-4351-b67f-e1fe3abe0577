from django.contrib import admin
from .models import Category, Brand, Product, ProductRecommendation, UserFavorite

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name', 'slug', 'order', 'is_active')
    list_filter = ('is_active',)
    search_fields = ('name',)
    prepopulated_fields = {'slug': ('name',)}

@admin.register(Brand)
class BrandAdmin(admin.ModelAdmin):
    list_display = ('name', 'slug', 'is_active')
    list_filter = ('is_active',)
    search_fields = ('name',)
    prepopulated_fields = {'slug': ('name',)}

@admin.register(Product)
class ProductAdmin(admin.ModelAdmin):
    list_display = ('name', 'brand', 'category', 'price', 'rating', 'is_featured', 'is_active')
    list_filter = ('brand', 'category', 'hair_type_compatibility', 'is_featured', 'is_active')
    search_fields = ('name', 'brand__name')
    prepopulated_fields = {'slug': ('name',)}
    list_editable = ('is_featured', 'is_active')

@admin.register(ProductRecommendation)
class ProductRecommendationAdmin(admin.ModelAdmin):
    list_display = ('user', 'product', 'confidence_score', 'created_at')
    list_filter = ('confidence_score', 'created_at')
    search_fields = ('user__email', 'product__name')

@admin.register(UserFavorite)
class UserFavoriteAdmin(admin.ModelAdmin):
    list_display = ('user', 'product', 'created_at')
    list_filter = ('created_at',)
    search_fields = ('user__email', 'product__name')
