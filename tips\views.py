from rest_framework import generics, status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import SearchFilter, OrderingFilter
from django.utils import timezone
from .models import Tip, TipLike, TipFavorite, DailyTip
from .serializers import (
    TipListSerializer, TipDetailSerializer, DailyTipSerializer,
    TipLikeSerializer, TipFavoriteSerializer
)
from .filters import TipFilter

class TipListView(generics.ListAPIView):
    queryset = Tip.objects.filter(is_active=True)
    serializer_class = TipListSerializer
    permission_classes = [permissions.AllowAny]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_class = TipFilter
    search_fields = ['title', 'content', 'tags']
    ordering_fields = ['created_at', 'views_count', 'likes_count']
    ordering = ['-created_at']

class TipDetailView(generics.RetrieveAPIView):
    queryset = Tip.objects.filter(is_active=True)
    serializer_class = TipDetailSerializer
    permission_classes = [permissions.AllowAny]
    lookup_field = 'slug'
    
    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()
        instance.increment_views()
        serializer = self.get_serializer(instance)
        return Response(serializer.data)

class FeaturedTipsView(generics.ListAPIView):
    queryset = Tip.objects.filter(is_active=True, is_featured=True)
    serializer_class = TipListSerializer
    permission_classes = [permissions.AllowAny]

class DailyTipsView(generics.ListAPIView):
    serializer_class = DailyTipSerializer
    permission_classes = [permissions.AllowAny]
    
    def get_queryset(self):
        today = timezone.now().date()
        return DailyTip.objects.filter(
            date__lte=today,
            is_active=True
        ).order_by('-date')[:7]  # 7 derniers jours

class UserTipLikesView(generics.ListAPIView):
    serializer_class = TipLikeSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        return TipLike.objects.filter(user=self.request.user)

class UserTipFavoritesView(generics.ListAPIView):
    serializer_class = TipFavoriteSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        return TipFavorite.objects.filter(user=self.request.user)

@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def toggle_tip_like(request, tip_id):
    try:
        tip = Tip.objects.get(id=tip_id, is_active=True)
        like, created = TipLike.objects.get_or_create(
            user=request.user, tip=tip
        )
        
        if not created:
            like.delete()
            tip.likes_count = max(0, tip.likes_count - 1)
            tip.save(update_fields=['likes_count'])
            return Response({
                'message': 'Like retiré',
                'is_liked': False,
                'likes_count': tip.likes_count
            })
        else:
            tip.likes_count += 1
            tip.save(update_fields=['likes_count'])
            return Response({
                'message': 'Like ajouté',
                'is_liked': True,
                'likes_count': tip.likes_count
            })
    except Tip.DoesNotExist:
        return Response(
            {'error': 'Astuce non trouvée'}, 
            status=status.HTTP_404_NOT_FOUND
        )

@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def toggle_tip_favorite(request, tip_id):
    try:
        tip = Tip.objects.get(id=tip_id, is_active=True)
        favorite, created = TipFavorite.objects.get_or_create(
            user=request.user, tip=tip
        )
        
        if not created:
            favorite.delete()
            return Response({
                'message': 'Astuce retirée des favoris',
                'is_favorited': False
            })
        else:
            return Response({
                'message': 'Astuce ajoutée aux favoris',
                'is_favorited': True
            })
    except Tip.DoesNotExist:
        return Response(
            {'error': 'Astuce non trouvée'}, 
            status=status.HTTP_404_NOT_FOUND
        )

@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def personalized_tips(request):
    """Astuces personnalisées basées sur le profil utilisateur"""
    user = request.user
    
    # Logique de personnalisation basée sur le type de cheveux
    filters = {'is_active': True}
    
    if user.hair_type:
        # Filtrer par type de cheveux compatible
        hair_type_filters = []
        if user.hair_type.startswith('3') or user.hair_type.startswith('4'):
            hair_type_filters = ['3A', '3B', '3C', '4A', '4B', '4C']
        elif user.hair_type.startswith('2'):
            hair_type_filters = ['2A', '2B', '2C']
        elif user.hair_type.startswith('1'):
            hair_type_filters = ['1A', '1B', '1C']
        
        if hair_type_filters:
            tips = Tip.objects.filter(
                is_active=True
            ).filter(
                models.Q(hair_types__icontains=user.hair_type) |
                models.Q(hair_types='') |
                models.Q(hair_types__isnull=True)
            )[:10]
        else:
            tips = Tip.objects.filter(is_active=True)[:10]
    else:
        tips = Tip.objects.filter(is_active=True)[:10]
    
    serializer = TipListSerializer(
        tips, 
        many=True, 
        context={'request': request}
    )
    
    return Response({
        'message': 'Astuces personnalisées',
        'tips': serializer.data
    })

@api_view(['GET'])
@permission_classes([permissions.AllowAny])
def tips_by_category(request, category_slug):
    """Astuces par catégorie"""
    tips = Tip.objects.filter(
        category__slug=category_slug,
        is_active=True
    )
    
    serializer = TipListSerializer(
        tips, 
        many=True, 
        context={'request': request}
    )
    
    return Response({
        'category': category_slug,
        'tips': serializer.data
    })
