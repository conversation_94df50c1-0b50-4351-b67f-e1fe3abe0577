from rest_framework import serializers
from .models import Conversation, Message, ChatSession, UserPreference

class MessageSerializer(serializers.ModelSerializer):
    class Meta:
        model = Message
        fields = [
            'id', 'sender_type', 'message_type', 'content', 'audio_file',
            'ai_model', 'tokens_used', 'processing_time', 'created_at', 'is_read'
        ]
        read_only_fields = ['id', 'ai_model', 'tokens_used', 'processing_time', 'created_at']

class ConversationSerializer(serializers.ModelSerializer):
    messages = MessageSerializer(many=True, read_only=True)
    message_count = serializers.SerializerMethodField()
    last_message = serializers.SerializerMethodField()
    
    class Meta:
        model = Conversation
        fields = [
            'id', 'title', 'created_at', 'updated_at', 'is_active',
            'messages', 'message_count', 'last_message'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def get_message_count(self, obj):
        return obj.messages.count()
    
    def get_last_message(self, obj):
        last_message = obj.messages.last()
        if last_message:
            return MessageSerializer(last_message).data
        return None

class ConversationListSerializer(serializers.ModelSerializer):
    message_count = serializers.SerializerMethodField()
    last_message = serializers.SerializerMethodField()
    
    class Meta:
        model = Conversation
        fields = [
            'id', 'title', 'created_at', 'updated_at', 'is_active',
            'message_count', 'last_message'
        ]
    
    def get_message_count(self, obj):
        return obj.messages.count()
    
    def get_last_message(self, obj):
        last_message = obj.messages.last()
        if last_message:
            return {
                'content': last_message.content[:100],
                'sender_type': last_message.sender_type,
                'created_at': last_message.created_at
            }
        return None

class ChatSessionSerializer(serializers.ModelSerializer):
    class Meta:
        model = ChatSession
        fields = [
            'id', 'conversation', 'is_active', 'started_at', 'ended_at',
            'voice_enabled', 'interruption_enabled'
        ]
        read_only_fields = ['id', 'started_at', 'ended_at']

class UserPreferenceSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserPreference
        fields = [
            'voice_speed', 'voice_pitch', 'preferred_voice', 'conversation_style',
            'sound_notifications', 'vibration_notifications'
        ]

class ChatMessageCreateSerializer(serializers.Serializer):
    content = serializers.CharField()
    message_type = serializers.ChoiceField(choices=Message.MESSAGE_TYPES, default='TEXT')
    audio_file = serializers.FileField(required=False)
    
    def validate(self, attrs):
        if attrs.get('message_type') == 'VOICE' and not attrs.get('audio_file'):
            raise serializers.ValidationError("Audio file required for voice messages")
        return attrs
