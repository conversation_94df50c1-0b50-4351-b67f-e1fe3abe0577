from django.contrib import admin
from .models import Conversation, Message, ChatSession, UserPreference

@admin.register(Conversation)
class ConversationAdmin(admin.ModelAdmin):
    list_display = ('id', 'user', 'title', 'created_at', 'is_active')
    list_filter = ('is_active', 'created_at')
    search_fields = ('user__email', 'title')
    readonly_fields = ('id', 'created_at', 'updated_at')

@admin.register(Message)
class MessageAdmin(admin.ModelAdmin):
    list_display = ('id', 'conversation', 'sender_type', 'message_type', 'created_at')
    list_filter = ('sender_type', 'message_type', 'created_at')
    search_fields = ('content', 'conversation__user__email')
    readonly_fields = ('id', 'created_at')

@admin.register(ChatSession)
class ChatSessionAdmin(admin.ModelAdmin):
    list_display = ('id', 'user', 'is_active', 'started_at', 'ended_at')
    list_filter = ('is_active', 'voice_enabled', 'started_at')
    search_fields = ('user__email',)
    readonly_fields = ('id', 'started_at', 'ended_at')

@admin.register(UserPreference)
class UserPreferenceAdmin(admin.ModelAdmin):
    list_display = ('user', 'conversation_style', 'preferred_voice', 'sound_notifications')
    list_filter = ('conversation_style', 'sound_notifications', 'vibration_notifications')
    search_fields = ('user__email',)
