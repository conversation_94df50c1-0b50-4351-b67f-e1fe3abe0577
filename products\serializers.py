from rest_framework import serializers
from .models import Category, Brand, Product, ProductRecommendation, UserFavorite

class CategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = Category
        fields = ['id', 'name', 'slug', 'description', 'icon', 'order']

class BrandSerializer(serializers.ModelSerializer):
    class Meta:
        model = Brand
        fields = ['id', 'name', 'slug', 'logo', 'description', 'website']

class ProductListSerializer(serializers.ModelSerializer):
    brand = BrandSerializer(read_only=True)
    category = CategorySerializer(read_only=True)
    is_favorited = serializers.SerializerMethodField()
    
    class Meta:
        model = Product
        fields = [
            'id', 'name', 'slug', 'brand', 'category', 'short_description',
            'main_image', 'price', 'original_price', 'is_on_sale', 
            'discount_percentage', 'rating', 'review_count', 'is_favorited'
        ]
    
    def get_is_favorited(self, obj):
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            return UserFavorite.objects.filter(user=request.user, product=obj).exists()
        return False

class ProductDetailSerializer(serializers.ModelSerializer):
    brand = BrandSerializer(read_only=True)
    category = CategorySerializer(read_only=True)
    is_favorited = serializers.SerializerMethodField()
    
    class Meta:
        model = Product
        fields = [
            'id', 'name', 'slug', 'brand', 'category', 'description',
            'short_description', 'ingredients', 'benefits', 'usage_instructions',
            'main_image', 'image_2', 'image_3', 'price', 'original_price',
            'is_on_sale', 'discount_percentage', 'hair_type_compatibility',
            'porosity_compatibility', 'purchase_url', 'rating', 'review_count',
            'is_favorited', 'created_at'
        ]
    
    def get_is_favorited(self, obj):
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            return UserFavorite.objects.filter(user=request.user, product=obj).exists()
        return False

class ProductRecommendationSerializer(serializers.ModelSerializer):
    product = ProductListSerializer(read_only=True)
    
    class Meta:
        model = ProductRecommendation
        fields = ['id', 'product', 'reason', 'confidence_score', 'created_at']

class UserFavoriteSerializer(serializers.ModelSerializer):
    product = ProductListSerializer(read_only=True)
    
    class Meta:
        model = UserFavorite
        fields = ['id', 'product', 'created_at']
