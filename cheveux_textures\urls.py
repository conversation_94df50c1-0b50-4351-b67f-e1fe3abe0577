from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from rest_framework import permissions
from drf_yasg.views import get_schema_view
from drf_yasg import openapi

# Configuration Swagger
schema_view = get_schema_view(
    openapi.Info(
        title="Cheveux Texturés API",
        default_version='v1',
        description="""
        # API Documentation - Cheveux Texturés
        
        ## Description
        API complète pour l'application mobile Cheveux Texturés - Votre coach capillaire IA personnel.
        
        ## Authentification
        Cette API utilise JWT (JSON Web Tokens) pour l'authentification.
        
        ### Comment s'authentifier :
        1. Créez un compte avec `/api/auth/register/`
        2. Connectez-vous avec `/api/auth/login/` pour obtenir vos tokens
        3. Utilisez le token d'accès dans l'en-tête : `Authorization: Bearer <votre_token>`
        
        ## Fonctionnalités principales
        - 🤖 **Chat IA** : Conversation avec un coach capillaire spécialisé
        - 👤 **Profil utilisateur** : Gestion complète du profil et préférences
        - 🛍️ **Produits** : Catalogue avec recommandations personnalisées
        - 💡 **Astuces** : Conseils capillaires adaptés à votre profil
        - 🎁 **Récompenses** : Système de points et challenges
        - 📱 **Notifications** : Push, email et in-app
        - 🚀 **Onboarding** : Processus d'accueil personnalisé
        
        ## Codes d'erreur
        - `400` : Erreur de validation des données
        - `401` : Non authentifié
        - `403` : Accès refusé
        - `404` : Ressource non trouvée
        - `429` : Limite de taux dépassée
        - `500` : Erreur serveur
        
        ## Support
        Pour toute question : <EMAIL>
        """,
        terms_of_service="https://cheveuxextures.com/terms/",
        contact=openapi.Contact(email="<EMAIL>"),
        license=openapi.License(name="Proprietary License"),
    ),
    public=True,
    permission_classes=[permissions.AllowAny],
    authentication_classes=[],
)

urlpatterns = [
    # Admin
    path('admin/', admin.site.urls),
    
    # Documentation API
    path('', schema_view.with_ui('redoc', cache_timeout=0), name='schema-redoc'),
    path('swagger/', schema_view.with_ui('swagger', cache_timeout=0), name='schema-swagger-ui'),
    path('swagger.json', schema_view.without_ui(cache_timeout=0), name='schema-json'),
    
    # API Endpoints
    path('api/auth/', include('accounts.urls')),
    path('api/products/', include('products.urls')),
    path('api/chat/', include('chat.urls')),
    path('api/tips/', include('tips.urls')),
    path('api/rewards/', include('rewards.urls')),
    path('api/onboarding/', include('onboarding.urls')),
    path('api/subscription/', include('subscription.urls')),
    path('api/analytics/', include('analytics.urls')),
    path('api/marketplace/', include('marketplace.urls')),
    path('api/notifications/', include('notifications.urls')),
]

if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
