from django.contrib import admin
from .models import (
    RewardProgram, PointsTransaction, UserPoints, Reward, 
    UserReward, Challenge, UserChallenge
)

@admin.register(RewardProgram)
class RewardProgramAdmin(admin.ModelAdmin):
    list_display = ('name', 'is_active', 'start_date', 'end_date')
    list_filter = ('is_active', 'start_date')

@admin.register(PointsTransaction)
class PointsTransactionAdmin(admin.ModelAdmin):
    list_display = ('user', 'transaction_type', 'action_type', 'points', 'created_at')
    list_filter = ('transaction_type', 'action_type', 'created_at')
    search_fields = ('user__email', 'description')

@admin.register(UserPoints)
class UserPointsAdmin(admin.ModelAdmin):
    list_display = ('user', 'available_points', 'total_points', 'daily_streak')
    search_fields = ('user__email',)
    readonly_fields = ('created_at', 'updated_at')

@admin.register(Reward)
class RewardAdmin(admin.ModelAdmin):
    list_display = ('name', 'reward_type', 'points_cost', 'is_active', 'stock_quantity')
    list_filter = ('reward_type', 'is_active', 'valid_from')
    search_fields = ('name', 'description')

@admin.register(UserReward)
class UserRewardAdmin(admin.ModelAdmin):
    list_display = ('user', 'reward', 'status', 'redemption_code', 'redeemed_at')
    list_filter = ('status', 'redeemed_at')
    search_fields = ('user__email', 'reward__name', 'redemption_code')

@admin.register(Challenge)
class ChallengeAdmin(admin.ModelAdmin):
    list_display = ('name', 'points_reward', 'is_active', 'start_date', 'end_date')
    list_filter = ('is_active', 'start_date')
    search_fields = ('name', 'description')

@admin.register(UserChallenge)
class UserChallengeAdmin(admin.ModelAdmin):
    list_display = ('user', 'challenge', 'is_completed', 'completed_at')
    list_filter = ('is_completed', 'completed_at')
    search_fields = ('user__email', 'challenge__name')
