from django.core.management.base import BaseCommand
from django.utils import timezone
from accounts.models import User
from tips.models import DailyTip
from notifications.services import NotificationService

class Command(BaseCommand):
    help = 'Envoie les astuces du jour aux utilisateurs'
    
    def handle(self, *args, **options):
        today = timezone.now().date()
        
        # Récupérer l'astuce du jour
        try:
            daily_tip = DailyTip.objects.get(date=today, is_active=True)
        except DailyTip.DoesNotExist:
            self.stdout.write(
                self.style.WARNING('Aucune astuce du jour configurée')
            )
            return
        
        # Récupérer les utilisateurs actifs
        users = User.objects.filter(
            is_active=True,
            email_verified=True
        )
        
        sent_count = 0
        for user in users:
            # Vérifier les préférences de notification
            if hasattr(user, 'notification_settings'):
                if not user.notification_settings.tip_notifications:
                    continue
            
            # Envoyer la notification
            success = NotificationService.send_daily_tip_notification(
                user=user,
                tip_title=daily_tip.tip.title
            )
            
            if success:
                sent_count += 1
        
        self.stdout.write(
            self.style.SUCCESS(f'Astuce du jour envoyée à {sent_count} utilisateurs')
        )
