from django.db import models
from accounts.models import User

class OnboardingStep(models.Model):
    STEP_TYPES = [
        ('WELCOME', 'Bienvenue'),
        ('PROFILE', 'Profil'),
        ('HAIR_ANALYSIS', 'Analyse capillaire'),
        ('PREFERENCES', 'Préférences'),
        ('GOALS', 'Objectifs'),
        ('COMPLETION', 'Finalisation'),
    ]
    
    step_number = models.IntegerField()
    step_type = models.CharField(max_length=15, choices=STEP_TYPES)
    title = models.CharField(max_length=200)
    description = models.TextField()
    image = models.ImageField(upload_to='onboarding/', blank=True, null=True)
    
    # Configuration
    is_required = models.BooleanField(default=True)
    is_active = models.BooleanField(default=True)
    
    class Meta:
        ordering = ['step_number']
    
    def __str__(self):
        return f"Étape {self.step_number}: {self.title}"

class OnboardingQuestion(models.Model):
    QUESTION_TYPES = [
        ('SINGLE_CHOICE', 'Choix unique'),
        ('MULTIPLE_CHOICE', 'Choix multiple'),
        ('TEXT', 'Texte libre'),
        ('SCALE', 'Échelle'),
        ('IMAGE_CHOICE', 'Choix d\'image'),
    ]
    
    step = models.ForeignKey(OnboardingStep, on_delete=models.CASCADE, related_name='questions')
    question_number = models.IntegerField()
    question_type = models.CharField(max_length=15, choices=QUESTION_TYPES)
    question_text = models.TextField()
    help_text = models.TextField(blank=True)
    
    # Configuration
    is_required = models.BooleanField(default=True)
    
    class Meta:
        ordering = ['question_number']
        unique_together = ['step', 'question_number']
    
    def __str__(self):
        return f"{self.step.title} - Q{self.question_number}"

class QuestionOption(models.Model):
    question = models.ForeignKey(OnboardingQuestion, on_delete=models.CASCADE, related_name='options')
    option_text = models.CharField(max_length=200)
    option_value = models.CharField(max_length=100)
    option_image = models.ImageField(upload_to='onboarding/options/', blank=True, null=True)
    order = models.IntegerField(default=0)
    
    class Meta:
        ordering = ['order']
    
    def __str__(self):
        return f"{self.question} - {self.option_text}"

class UserOnboardingProgress(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='onboarding_progress')
    current_step = models.ForeignKey(OnboardingStep, on_delete=models.CASCADE, null=True, blank=True)
    completed_steps = models.JSONField(default=list)
    is_completed = models.BooleanField(default=False)
    
    started_at = models.DateTimeField(auto_now_add=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    
    def __str__(self):
        return f"Onboarding de {self.user.nom}"
    
    @property
    def progress_percentage(self):
        total_steps = OnboardingStep.objects.filter(is_active=True, is_required=True).count()
        if total_steps == 0:
            return 100
        return (len(self.completed_steps) / total_steps) * 100

class UserOnboardingAnswer(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='onboarding_answers')
    question = models.ForeignKey(OnboardingQuestion, on_delete=models.CASCADE)
    
    # Réponses possibles selon le type de question
    text_answer = models.TextField(blank=True)
    selected_options = models.JSONField(default=list)  # Pour choix multiples
    scale_value = models.IntegerField(null=True, blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        unique_together = ['user', 'question']
    
    def __str__(self):
        return f"{self.user.nom} - {self.question}"

class HairAnalysisResult(models.Model):
    """Résultat de l'analyse capillaire basée sur l'onboarding"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='hair_analysis')
    
    # Résultats calculés
    hair_type_detected = models.CharField(max_length=2, choices=User.HAIR_TYPES)
    porosity_detected = models.CharField(max_length=10, choices=User.POROSITY_CHOICES)
    
    # Recommandations
    recommended_routine = models.TextField()
    recommended_products = models.JSONField(default=list)
    care_tips = models.TextField()
    
    # Scores de confiance
    hair_type_confidence = models.DecimalField(max_digits=3, decimal_places=2, default=0.0)
    porosity_confidence = models.DecimalField(max_digits=3, decimal_places=2, default=0.0)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"Analyse de {self.user.nom}"
