# Generated by Django 4.2.7 on 2025-06-26 07:47

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('products', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Tip',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('slug', models.SlugField(unique=True)),
                ('content', models.TextField()),
                ('short_description', models.CharField(max_length=300)),
                ('difficulty', models.CharField(choices=[('BEGINNER', 'Débutant'), ('INTERMEDIATE', 'Intermédiaire'), ('ADVANCED', 'Avancé')], default='BEGINNER', max_length=15)),
                ('featured_image', models.ImageField(blank=True, null=True, upload_to='tips/')),
                ('video_url', models.URLField(blank=True)),
                ('hair_types', models.CharField(blank=True, help_text='Types de cheveux compatibles (ex: 3A,3B,4A)', max_length=100)),
                ('is_featured', models.BooleanField(default=False)),
                ('is_active', models.BooleanField(default=True)),
                ('views_count', models.IntegerField(default=0)),
                ('likes_count', models.IntegerField(default=0)),
                ('meta_description', models.CharField(blank=True, max_length=160)),
                ('tags', models.CharField(blank=True, help_text='Tags séparés par des virgules', max_length=200)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='tips', to='products.category')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='DailyTip',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField(unique=True)),
                ('is_active', models.BooleanField(default=True)),
                ('tip', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='tips.tip')),
            ],
            options={
                'ordering': ['-date'],
            },
        ),
        migrations.CreateModel(
            name='TipStep',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('step_number', models.IntegerField()),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('image', models.ImageField(blank=True, null=True, upload_to='tips/steps/')),
                ('tip', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='steps', to='tips.tip')),
            ],
            options={
                'ordering': ['step_number'],
                'unique_together': {('tip', 'step_number')},
            },
        ),
        migrations.CreateModel(
            name='TipLike',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('tip', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='likes', to='tips.tip')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='tip_likes', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'unique_together': {('user', 'tip')},
            },
        ),
        migrations.CreateModel(
            name='TipFavorite',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('tip', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='favorites', to='tips.tip')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='tip_favorites', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'unique_together': {('user', 'tip')},
            },
        ),
    ]
