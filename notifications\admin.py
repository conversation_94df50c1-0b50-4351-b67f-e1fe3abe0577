from django.contrib import admin
from .models import NotificationTemplate, Notification, UserNotificationSettings

@admin.register(NotificationTemplate)
class NotificationTemplateAdmin(admin.ModelAdmin):
    list_display = ('name', 'notification_type', 'is_active', 'send_push', 'send_email')
    list_filter = ('notification_type', 'is_active', 'send_push', 'send_email')
    search_fields = ('name', 'title_template')

@admin.register(Notification)
class NotificationAdmin(admin.ModelAdmin):
    list_display = ('title', 'user', 'notification_type', 'status', 'created_at')
    list_filter = ('notification_type', 'status', 'created_at')
    search_fields = ('title', 'user__email')
    readonly_fields = ('created_at', 'sent_at', 'read_at')

@admin.register(UserNotificationSettings)
class UserNotificationSettingsAdmin(admin.ModelAdmin):
    list_display = ('user', 'push_notifications', 'email_notifications', 'chat_notifications')
    list_filter = ('push_notifications', 'email_notifications', 'chat_notifications')
    search_fields = ('user__email', 'user__first_name', 'user__last_name')
