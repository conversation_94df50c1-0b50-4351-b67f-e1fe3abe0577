# Generated by Django 4.2.7 on 2025-06-26 07:47

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Challenge',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField()),
                ('points_reward', models.IntegerField()),
                ('required_actions', models.JSONField(default=dict, help_text='Actions requises pour compléter le défi')),
                ('is_active', models.BooleanField(default=True)),
                ('start_date', models.DateTimeField()),
                ('end_date', models.DateTimeField()),
                ('max_completions_per_user', models.IntegerField(default=1)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='Reward',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField()),
                ('reward_type', models.CharField(choices=[('DISCOUNT', 'Réduction'), ('FREEBIE', 'Cadeau gratuit'), ('PREMIUM', 'Accès premium'), ('CONSULTATION', 'Consultation'), ('EBOOK', 'E-book'), ('COURSE', 'Cours')], max_length=15)),
                ('points_cost', models.IntegerField()),
                ('discount_percentage', models.IntegerField(blank=True, null=True)),
                ('discount_amount', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('external_url', models.URLField(blank=True)),
                ('coupon_code', models.CharField(blank=True, max_length=50)),
                ('image', models.ImageField(blank=True, null=True, upload_to='rewards/')),
                ('is_active', models.BooleanField(default=True)),
                ('stock_quantity', models.IntegerField(blank=True, help_text='Laisser vide pour stock illimité', null=True)),
                ('valid_from', models.DateTimeField()),
                ('valid_until', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ['points_cost'],
            },
        ),
        migrations.CreateModel(
            name='RewardProgram',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField()),
                ('is_active', models.BooleanField(default=True)),
                ('start_date', models.DateTimeField()),
                ('end_date', models.DateTimeField(blank=True, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='UserReward',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('PENDING', 'En attente'), ('ACTIVE', 'Actif'), ('USED', 'Utilisé'), ('EXPIRED', 'Expiré')], default='PENDING', max_length=10)),
                ('redemption_code', models.CharField(max_length=20, unique=True)),
                ('redeemed_at', models.DateTimeField(auto_now_add=True)),
                ('expires_at', models.DateTimeField(blank=True, null=True)),
                ('used_at', models.DateTimeField(blank=True, null=True)),
                ('reward', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='user_rewards', to='rewards.reward')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='rewards', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='UserPoints',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('total_points', models.IntegerField(default=0)),
                ('available_points', models.IntegerField(default=0)),
                ('lifetime_points', models.IntegerField(default=0)),
                ('daily_streak', models.IntegerField(default=0)),
                ('last_login_date', models.DateField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='points', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='PointsTransaction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('transaction_type', models.CharField(choices=[('EARN', 'Gagné'), ('SPEND', 'Dépensé'), ('BONUS', 'Bonus'), ('PENALTY', 'Pénalité')], max_length=10)),
                ('action_type', models.CharField(choices=[('DAILY_LOGIN', 'Connexion quotidienne'), ('CHAT_MESSAGE', 'Message dans le chat'), ('TIP_LIKE', 'Like sur astuce'), ('TIP_FAVORITE', 'Astuce en favori'), ('PRODUCT_FAVORITE', 'Produit en favori'), ('PROFILE_COMPLETE', 'Profil complété'), ('ONBOARDING_COMPLETE', 'Onboarding terminé'), ('REFERRAL', 'Parrainage'), ('REWARD_REDEEM', 'Échange de récompense'), ('PREMIUM_UPGRADE', 'Passage premium')], max_length=20)),
                ('points', models.IntegerField()),
                ('description', models.CharField(max_length=200)),
                ('related_object_id', models.IntegerField(blank=True, null=True)),
                ('related_object_type', models.CharField(blank=True, max_length=50)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='points_transactions', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='UserChallenge',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('progress', models.JSONField(default=dict)),
                ('is_completed', models.BooleanField(default=False)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('challenge', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='user_challenges', to='rewards.challenge')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='challenges', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'unique_together': {('user', 'challenge')},
            },
        ),
    ]
