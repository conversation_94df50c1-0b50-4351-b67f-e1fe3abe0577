from django.urls import path
from . import views

urlpatterns = [
    # Inscription et connexion selon maquettes
    path('register/', views.register, name='register'),
    path('login/', views.login, name='login'),
    path('logout/', views.logout, name='logout'),
    
    # Vérification email avec code 6 chiffres selon maquette
    path('verify-email/', views.verify_email, name='verify_email'),
    path('resend-verification-code/', views.resend_verification_code, name='resend_verification_code'),
    
    # Profil utilisateur selon maquette
    path('profile/', views.UserProfileView.as_view(), name='profile'),
    
    # Changement de mot de passe selon maquette
    path('change-password/', views.change_password, name='change_password'),
    
    # Réinitialisation mot de passe avec code 6 chiffres selon maquette
    path('password-reset/request/', views.password_reset_request, name='password_reset_request'),
    path('password-reset/verify/', views.password_reset_verify, name='password_reset_verify'),
    path('password-reset/confirm/', views.password_reset_confirm, name='password_reset_confirm'),
]
