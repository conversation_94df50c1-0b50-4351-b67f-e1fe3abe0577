from django.contrib import admin
from .models import (
    OnboardingStep, OnboardingQuestion, QuestionOption,
    UserOnboardingProgress, UserOnboardingAnswer, HairAnalysisResult
)

class QuestionOptionInline(admin.TabularInline):
    model = QuestionOption
    extra = 1

class OnboardingQuestionInline(admin.TabularInline):
    model = OnboardingQuestion
    extra = 1

@admin.register(OnboardingStep)
class OnboardingStepAdmin(admin.ModelAdmin):
    list_display = ('step_number', 'title', 'step_type', 'is_required', 'is_active')
    list_filter = ('step_type', 'is_required', 'is_active')
    search_fields = ('title', 'description')
    inlines = [OnboardingQuestionInline]

@admin.register(OnboardingQuestion)
class OnboardingQuestionAdmin(admin.ModelAdmin):
    list_display = ('step', 'question_number', 'question_type', 'is_required')
    list_filter = ('question_type', 'is_required', 'step__step_type')
    search_fields = ('question_text',)
    inlines = [QuestionOptionInline]

@admin.register(UserOnboardingProgress)
class UserOnboardingProgressAdmin(admin.ModelAdmin):
    list_display = ('user', 'current_step', 'is_completed', 'progress_percentage')
    list_filter = ('is_completed', 'started_at')
    search_fields = ('user__email',)
    readonly_fields = ('progress_percentage',)

@admin.register(UserOnboardingAnswer)
class UserOnboardingAnswerAdmin(admin.ModelAdmin):
    list_display = ('user', 'question', 'created_at')
    list_filter = ('question__question_type', 'created_at')
    search_fields = ('user__email', 'question__question_text')

@admin.register(HairAnalysisResult)
class HairAnalysisResultAdmin(admin.ModelAdmin):
    list_display = ('user', 'hair_type_detected', 'porosity_detected', 'created_at')
    list_filter = ('hair_type_detected', 'porosity_detected', 'created_at')
    search_fields = ('user__email',)
    readonly_fields = ('created_at', 'updated_at')
