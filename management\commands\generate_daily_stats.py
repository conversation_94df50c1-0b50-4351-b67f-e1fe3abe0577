from django.core.management.base import BaseCommand
from django.utils import timezone
from django.db.models import Count, Sum
from datetime import timedelta
from accounts.models import User
from analytics.models import UserEvent, DailyStats, ConversionFunnel
from subscription.models import PaymentHistory

class Command(BaseCommand):
    help = 'Génère les statistiques quotidiennes'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--date',
            type=str,
            help='Date au format YYYY-MM-DD (par défaut: hier)',
        )
    
    def handle(self, *args, **options):
        if options['date']:
            from datetime import datetime
            date = datetime.strptime(options['date'], '%Y-%m-%d').date()
        else:
            date = timezone.now().date() - timedelta(days=1)
        
        self.stdout.write(f'Génération des stats pour {date}...')
        
        # Statistiques utilisateurs
        total_users = User.objects.filter(date_joined__date__lte=date).count()
        new_users = User.objects.filter(date_joined__date=date).count()
        active_users = UserEvent.objects.filter(
            created_at__date=date
        ).values('user').distinct().count()
        premium_users = User.objects.filter(
            is_premium=True,
            date_joined__date__lte=date
        ).count()
        
        # Statistiques d'engagement
        total_sessions = UserEvent.objects.filter(
            event_type='APP_OPEN',
            created_at__date=date
        ).count()
        
        total_chat_messages = UserEvent.objects.filter(
            event_type='CHAT_MESSAGE',
            created_at__date=date
        ).count()
        
        total_voice_messages = UserEvent.objects.filter(
            event_type='VOICE_MESSAGE',
            created_at__date=date
        ).count()
        
        # Statistiques contenu
        tips_viewed = UserEvent.objects.filter(
            event_type='TIP_VIEW',
            created_at__date=date
        ).count()
        
        products_viewed = UserEvent.objects.filter(
            event_type='PRODUCT_VIEW',
            created_at__date=date
        ).count()
        
        # Revenus
        revenue_data = PaymentHistory.objects.filter(
            created_at__date=date,
            status='COMPLETED'
        ).aggregate(
            total=Sum('amount'),
            count=Count('id')
        )
        
        total_revenue = revenue_data['total'] or 0
        new_subscriptions = revenue_data['count'] or 0
        
        # Créer ou mettre à jour les stats
        stats, created = DailyStats.objects.get_or_create(
            date=date,
            defaults={
                'total_users': total_users,
                'new_users': new_users,
                'active_users': active_users,
                'premium_users': premium_users,
                'total_sessions': total_sessions,
                'total_chat_messages': total_chat_messages,
                'total_voice_messages': total_voice_messages,
                'tips_viewed': tips_viewed,
                'products_viewed': products_viewed,
                'total_revenue': total_revenue,
                'new_subscriptions': new_subscriptions,
            }
        )
        
        if not created:
            stats.total_users = total_users
            stats.new_users = new_users
            stats.active_users = active_users
            stats.premium_users = premium_users
            stats.total_sessions = total_sessions
            stats.total_chat_messages = total_chat_messages
            stats.total_voice_messages = total_voice_messages
            stats.tips_viewed = tips_viewed
            stats.products_viewed = products_viewed
            stats.total_revenue = total_revenue
            stats.new_subscriptions = new_subscriptions
            stats.save()
        
        self.stdout.write(
            self.style.SUCCESS(f'Stats générées: {new_users} nouveaux utilisateurs, {total_revenue}€ de revenus')
        )
