from rest_framework import generics, status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django.utils import timezone
from .models import (
    OnboardingStep, OnboardingQuestion, UserOnboardingProgress,
    UserOnboardingAnswer, HairAnalysisResult
)
from .serializers import (
    OnboardingStepSerializer, UserOnboardingProgressSerializer,
    UserOnboardingAnswerSerializer, SubmitAnswerSerializer,
    HairAnalysisResultSerializer
)
from .analysis_service import HairAnalysisService

class OnboardingStepsView(generics.ListAPIView):
    serializer_class = OnboardingStepSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        return OnboardingStep.objects.filter(is_active=True)

class UserOnboardingProgressView(generics.RetrieveAPIView):
    serializer_class = UserOnboardingProgressSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_object(self):
        progress, created = UserOnboardingProgress.objects.get_or_create(
            user=self.request.user
        )
        if created:
            # Définir la première étape
            first_step = OnboardingStep.objects.filter(is_active=True).first()
            if first_step:
                progress.current_step = first_step
                progress.save()
        return progress

@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def submit_answer(request):
    """Soumettre une réponse à une question d'onboarding"""
    serializer = SubmitAnswerSerializer(data=request.data)
    if not serializer.is_valid():
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    question_id = serializer.validated_data['question_id']
    question = OnboardingQuestion.objects.get(id=question_id)
    
    # Créer ou mettre à jour la réponse
    answer, created = UserOnboardingAnswer.objects.get_or_create(
        user=request.user,
        question=question,
        defaults={
            'text_answer': serializer.validated_data.get('text_answer', ''),
            'selected_options': serializer.validated_data.get('selected_options', []),
            'scale_value': serializer.validated_data.get('scale_value')
        }
    )
    
    if not created:
        answer.text_answer = serializer.validated_data.get('text_answer', '')
        answer.selected_options = serializer.validated_data.get('selected_options', [])
        answer.scale_value = serializer.validated_data.get('scale_value')
        answer.save()
    
    return Response({
        'message': 'Réponse enregistrée',
        'answer': UserOnboardingAnswerSerializer(answer).data
    })

@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def complete_step(request, step_id):
    """Marquer une étape comme terminée"""
    try:
        step = OnboardingStep.objects.get(id=step_id, is_active=True)
    except OnboardingStep.DoesNotExist:
        return Response(
            {'error': 'Étape non trouvée'}, 
            status=status.HTTP_404_NOT_FOUND
        )
    
    progress, created = UserOnboardingProgress.objects.get_or_create(
        user=request.user
    )
    
    # Ajouter l'étape aux étapes complétées si pas déjà présente
    if step.id not in progress.completed_steps:
        progress.completed_steps.append(step.id)
    
    # Passer à l'étape suivante
    next_step = OnboardingStep.objects.filter(
        step_number__gt=step.step_number,
        is_active=True
    ).first()
    
    if next_step:
        progress.current_step = next_step
    else:
        # Onboarding terminé
        progress.is_completed = True
        progress.completed_at = timezone.now()
        progress.current_step = None
        
        # Mettre à jour le profil utilisateur
        request.user.onboarding_completed = True
        request.user.save()
        
        # Déclencher l'analyse capillaire
        analysis_service = HairAnalysisService()
        analysis_service.analyze_user_hair(request.user)
    
    progress.save()
    
    return Response({
        'message': 'Étape terminée',
        'progress': UserOnboardingProgressSerializer(progress).data,
        'is_onboarding_complete': progress.is_completed
    })

@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def skip_step(request, step_id):
    """Passer une étape optionnelle"""
    try:
        step = OnboardingStep.objects.get(id=step_id, is_active=True)
    except OnboardingStep.DoesNotExist:
        return Response(
            {'error': 'Étape non trouvée'}, 
            status=status.HTTP_404_NOT_FOUND
        )
    
    if step.is_required:
        return Response(
            {'error': 'Cette étape est obligatoire'}, 
            status=status.HTTP_400_BAD_REQUEST
        )
    
    # Utiliser la même logique que complete_step
    return complete_step(request, step_id)

@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def restart_onboarding(request):
    """Redémarrer l'onboarding"""
    try:
        progress = UserOnboardingProgress.objects.get(user=request.user)
        progress.completed_steps = []
        progress.is_completed = False
        progress.completed_at = None
        
        # Revenir à la première étape
        first_step = OnboardingStep.objects.filter(is_active=True).first()
        progress.current_step = first_step
        progress.save()
        
        # Supprimer les anciennes réponses
        UserOnboardingAnswer.objects.filter(user=request.user).delete()
        
        # Supprimer l'ancienne analyse
        HairAnalysisResult.objects.filter(user=request.user).delete()
        
        request.user.onboarding_completed = False
        request.user.save()
        
        return Response({
            'message': 'Onboarding redémarré',
            'progress': UserOnboardingProgressSerializer(progress).data
        })
        
    except UserOnboardingProgress.DoesNotExist:
        return Response(
            {'error': 'Aucun onboarding trouvé'}, 
            status=status.HTTP_404_NOT_FOUND
        )

class HairAnalysisResultView(generics.RetrieveAPIView):
    serializer_class = HairAnalysisResultSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_object(self):
        try:
            return HairAnalysisResult.objects.get(user=self.request.user)
        except HairAnalysisResult.DoesNotExist:
            # Déclencher une nouvelle analyse si elle n'existe pas
            analysis_service = HairAnalysisService()
            return analysis_service.analyze_user_hair(self.request.user)

@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def onboarding_summary(request):
    """Résumé de l'onboarding de l'utilisateur"""
    try:
        progress = UserOnboardingProgress.objects.get(user=request.user)
        answers = UserOnboardingAnswer.objects.filter(user=request.user)
        
        summary = {
            'progress': UserOnboardingProgressSerializer(progress).data,
            'total_answers': answers.count(),
            'answers_by_step': {}
        }
        
        # Grouper les réponses par étape
        for answer in answers:
            step_id = answer.question.step.id
            if step_id not in summary['answers_by_step']:
                summary['answers_by_step'][step_id] = []
            summary['answers_by_step'][step_id].append(
                UserOnboardingAnswerSerializer(answer).data
            )
        
        return Response(summary)
        
    except UserOnboardingProgress.DoesNotExist:
        return Response({
            'progress': None,
            'total_answers': 0,
            'answers_by_step': {}
        })
